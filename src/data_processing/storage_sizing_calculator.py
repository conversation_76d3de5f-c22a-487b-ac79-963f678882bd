"""
储能规格计算模块
基于CityLearn数据集的储能配置比例，为其他数据集计算合适的储能规格
"""

import pandas as pd
import numpy as np
import json
import os
from typing import Dict, List, Tuple, Optional
from pathlib import Path


class StorageSizingCalculator:
    """储能规格计算器"""
    
    def __init__(self, base_dataset_path: str = "dataset/citylearn_challenge_2022_phase_all_plus_evs"):
        self.base_dataset_path = base_dataset_path
        self.citylearn_config = self._load_citylearn_config()
        self.sizing_ratios = self._calculate_sizing_ratios()
        
    def _load_citylearn_config(self) -> Dict:
        """加载CityLearn配置"""
        schema_path = os.path.join(self.base_dataset_path, "schema.json")
        with open(schema_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _calculate_sizing_ratios(self) -> Dict:
        """计算CityLearn储能配置比例"""
        buildings = self.citylearn_config['buildings']
        ratios = {
            'capacity_to_pv_ratio': [],
            'power_to_pv_ratio': [],
            'capacity_to_load_ratio': [],
            'power_to_load_ratio': []
        }
        
        for building_name, building_config in buildings.items():
            # 获取储能和光伏配置
            storage_capacity = building_config['electrical_storage']['attributes']['capacity']  # 6.4 kWh
            storage_power = building_config['electrical_storage']['attributes']['nominal_power']  # 5.0 kW
            pv_power = building_config['pv']['attributes']['nominal_power']  # 4-15 kW
            
            # 计算比例
            ratios['capacity_to_pv_ratio'].append(storage_capacity / pv_power)
            ratios['power_to_pv_ratio'].append(storage_power / pv_power)
            
            # 加载建筑负荷数据计算负荷比例
            building_data = self._load_building_data(building_name)
            if building_data is not None:
                avg_load = self._calculate_average_load(building_data)
                peak_load = self._calculate_peak_load(building_data)
                
                ratios['capacity_to_load_ratio'].append(storage_capacity / avg_load)
                ratios['power_to_load_ratio'].append(storage_power / peak_load)
        
        # 计算平均比例
        return {
            'avg_capacity_to_pv_ratio': np.mean(ratios['capacity_to_pv_ratio']),
            'avg_power_to_pv_ratio': np.mean(ratios['power_to_pv_ratio']),
            'avg_capacity_to_load_ratio': np.mean(ratios['capacity_to_load_ratio']),
            'avg_power_to_load_ratio': np.mean(ratios['power_to_load_ratio']),
            'std_capacity_to_pv_ratio': np.std(ratios['capacity_to_pv_ratio']),
            'std_power_to_pv_ratio': np.std(ratios['power_to_pv_ratio'])
        }
    
    def _load_building_data(self, building_name: str) -> Optional[pd.DataFrame]:
        """加载建筑数据"""
        try:
            data_path = os.path.join(self.base_dataset_path, f"{building_name}.csv")
            return pd.read_csv(data_path)
        except Exception as e:
            print(f"无法加载建筑数据 {building_name}: {e}")
            return None
    
    def _calculate_average_load(self, building_data: pd.DataFrame) -> float:
        """计算平均负荷"""
        total_load = (building_data['non_shiftable_load'] + 
                     building_data['cooling_demand'] + 
                     building_data['heating_demand'] + 
                     building_data['dhw_demand'])
        return total_load.mean()
    
    def _calculate_peak_load(self, building_data: pd.DataFrame) -> float:
        """计算峰值负荷"""
        total_load = (building_data['non_shiftable_load'] + 
                     building_data['cooling_demand'] + 
                     building_data['heating_demand'] + 
                     building_data['dhw_demand'])
        return total_load.max()
    
    def calculate_storage_specs(self, dataset_path: str, method: str = "pv_based") -> Dict:
        """
        为指定数据集计算储能规格
        
        Args:
            dataset_path: 数据集路径
            method: 计算方法 ("pv_based", "load_based", "hybrid")
        
        Returns:
            储能规格字典
        """
        schema_path = os.path.join(dataset_path, "schema.json")
        with open(schema_path, 'r', encoding='utf-8') as f:
            schema = json.load(f)
        
        storage_specs = {}
        buildings = schema['buildings']
        
        for building_name, building_config in buildings.items():
            # 获取光伏配置
            pv_config = building_config.get('pv', {})
            if pv_config.get('autosize', False):
                # 从autosize_attributes获取屋顶面积
                roof_area = pv_config.get('autosize_attributes', {}).get('roof_area', 200)
                # 假设光伏效率为20%，峰值日照1000W/m²
                estimated_pv_power = roof_area * 0.2  # kW
            else:
                estimated_pv_power = pv_config.get('attributes', {}).get('nominal_power', 10)
            
            # 根据方法计算储能规格
            if method == "pv_based":
                capacity = estimated_pv_power * self.sizing_ratios['avg_capacity_to_pv_ratio']
                power = estimated_pv_power * self.sizing_ratios['avg_power_to_pv_ratio']
            
            elif method == "load_based":
                # 加载建筑数据计算负荷
                building_data = self._load_other_dataset_building_data(dataset_path, building_name)
                if building_data is not None:
                    avg_load = self._calculate_average_load(building_data)
                    peak_load = self._calculate_peak_load(building_data)
                    capacity = avg_load * self.sizing_ratios['avg_capacity_to_load_ratio']
                    power = peak_load * self.sizing_ratios['avg_power_to_load_ratio']
                else:
                    # 回退到基于光伏的方法
                    capacity = estimated_pv_power * self.sizing_ratios['avg_capacity_to_pv_ratio']
                    power = estimated_pv_power * self.sizing_ratios['avg_power_to_pv_ratio']
            
            else:  # hybrid method
                # 结合光伏和负荷的混合方法
                pv_capacity = estimated_pv_power * self.sizing_ratios['avg_capacity_to_pv_ratio']
                pv_power = estimated_pv_power * self.sizing_ratios['avg_power_to_pv_ratio']
                
                building_data = self._load_other_dataset_building_data(dataset_path, building_name)
                if building_data is not None:
                    avg_load = self._calculate_average_load(building_data)
                    peak_load = self._calculate_peak_load(building_data)
                    load_capacity = avg_load * self.sizing_ratios['avg_capacity_to_load_ratio']
                    load_power = peak_load * self.sizing_ratios['avg_power_to_load_ratio']
                    
                    # 取两种方法的平均值
                    capacity = (pv_capacity + load_capacity) / 2
                    power = (pv_power + load_power) / 2
                else:
                    capacity = pv_capacity
                    power = pv_power
            
            # 确保合理的规格范围
            capacity = max(1.0, min(capacity, 50.0))  # 1-50 kWh
            power = max(0.5, min(power, 25.0))        # 0.5-25 kW
            
            storage_specs[building_name] = {
                "capacity": round(capacity, 1),
                "nominal_power": round(power, 1),
                "efficiency": 0.9,
                "capacity_loss_coefficient": 1e-05,
                "loss_coefficient": 0.0,
                "estimated_pv_power": round(estimated_pv_power, 1)
            }
        
        return storage_specs
    
    def _load_other_dataset_building_data(self, dataset_path: str, building_name: str) -> Optional[pd.DataFrame]:
        """加载其他数据集的建筑数据"""
        try:
            data_path = os.path.join(dataset_path, f"{building_name}.csv")
            return pd.read_csv(data_path)
        except Exception as e:
            print(f"无法加载建筑数据 {building_name}: {e}")
            return None
    
    def update_schema_with_storage_specs(self, dataset_path: str, storage_specs: Dict, 
                                       output_path: Optional[str] = None) -> None:
        """
        更新schema文件，添加储能规格
        
        Args:
            dataset_path: 数据集路径
            storage_specs: 储能规格字典
            output_path: 输出路径，如果为None则覆盖原文件
        """
        schema_path = os.path.join(dataset_path, "schema.json")
        with open(schema_path, 'r', encoding='utf-8') as f:
            schema = json.load(f)
        
        # 更新储能配置
        for building_name, specs in storage_specs.items():
            if building_name in schema['buildings']:
                schema['buildings'][building_name]['electrical_storage'] = {
                    "type": "citylearn.energy_model.Battery",
                    "autosize": False,
                    "attributes": {
                        "capacity": specs['capacity'],
                        "efficiency": specs['efficiency'],
                        "capacity_loss_coefficient": specs['capacity_loss_coefficient'],
                        "loss_coefficient": specs['loss_coefficient'],
                        "nominal_power": specs['nominal_power']
                    }
                }
        
        # 保存更新后的schema
        if output_path is None:
            output_path = schema_path
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(schema, f, indent=2, ensure_ascii=False)
        
        print(f"储能规格已更新到: {output_path}")
    
    def generate_storage_report(self, storage_specs: Dict) -> str:
        """生成储能规格报告"""
        report = "=== 储能规格计算报告 ===\n\n"
        report += f"基于CityLearn数据集的储能配置比例:\n"
        report += f"- 储能容量/光伏功率比例: {self.sizing_ratios['avg_capacity_to_pv_ratio']:.2f} ± {self.sizing_ratios['std_capacity_to_pv_ratio']:.2f}\n"
        report += f"- 储能功率/光伏功率比例: {self.sizing_ratios['avg_power_to_pv_ratio']:.2f} ± {self.sizing_ratios['std_power_to_pv_ratio']:.2f}\n\n"
        
        report += "计算得到的储能规格:\n"
        total_capacity = 0
        total_power = 0
        
        for building_name, specs in storage_specs.items():
            report += f"- {building_name}: {specs['capacity']} kWh / {specs['nominal_power']} kW "
            report += f"(估算光伏: {specs['estimated_pv_power']} kW)\n"
            total_capacity += specs['capacity']
            total_power += specs['nominal_power']
        
        report += f"\n总计: {total_capacity:.1f} kWh / {total_power:.1f} kW\n"
        report += f"平均每栋建筑: {total_capacity/len(storage_specs):.1f} kWh / {total_power/len(storage_specs):.1f} kW\n"
        
        return report


def main():
    """主函数 - 演示储能规格计算"""
    calculator = StorageSizingCalculator()
    
    # 为各个数据集计算储能规格
    datasets = [
        "dataset/tx_travis_county_neighborhood",
        "dataset/ca_alameda_county_neighborhood", 
        "dataset/vt_chittenden_county_neighborhood"
    ]
    
    for dataset_path in datasets:
        if os.path.exists(dataset_path):
            print(f"\n处理数据集: {dataset_path}")
            
            # 计算储能规格
            storage_specs = calculator.calculate_storage_specs(dataset_path, method="hybrid")
            
            # 生成报告
            report = calculator.generate_storage_report(storage_specs)
            print(report)
            
            # 更新schema文件
            output_path = os.path.join(dataset_path, "schema_with_storage.json")
            calculator.update_schema_with_storage_specs(dataset_path, storage_specs, output_path)


if __name__ == "__main__":
    main()
