"""
Nolan县风电资源分析器
分析德州Nolan县的风力数据，评估是否符合风电场开发标准
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple
import os


class NolanCountyWindAnalyzer:
    """Nolan县风电资源分析器"""

    def __init__(self, data_file: str = "dataset/G4803530_2018.csv"):
        self.data_file = data_file
        self.wind_data = None
        self.analysis_results = {}

        # 风电场开发标准 (基于现代风机技术和实际项目经验)
        self.wind_farm_standards = {
            'excellent': {
                'mean_wind_speed': 8.0,      # 平均风速 ≥ 8.0 m/s
                'capacity_factor': 0.35,     # 容量因子 ≥ 35%
                'wind_power_density': 500,   # 风功率密度 ≥ 500 W/m²
                'hours_above_cut_in': 7000   # 年有效风时 ≥ 7000小时
            },
            'good': {
                'mean_wind_speed': 6.5,      # 平均风速 ≥ 6.5 m/s
                'capacity_factor': 0.25,     # 容量因子 ≥ 25%
                'wind_power_density': 350,   # 风功率密度 ≥ 350 W/m²
                'hours_above_cut_in': 6000   # 年有效风时 ≥ 6000小时
            },
            'fair': {
                'mean_wind_speed': 5.5,      # 平均风速 ≥ 5.5 m/s
                'capacity_factor': 0.18,     # 容量因子 ≥ 18%
                'wind_power_density': 250,   # 风功率密度 ≥ 250 W/m²
                'hours_above_cut_in': 5000   # 年有效风时 ≥ 5000小时
            },
            'poor': {
                'mean_wind_speed': 0.0,      # 平均风速 < 5.5 m/s
                'capacity_factor': 0.0,      # 容量因子 < 18%
                'wind_power_density': 0.0,   # 风功率密度 < 250 W/m²
                'hours_above_cut_in': 0      # 年有效风时 < 5000小时
            }
        }

        # 现代低风速大型风机参数 (类似GE 2.5-120)
        self.wind_turbine_specs = {
            'rated_power': 2500,      # 额定功率 2.5MW
            'cut_in_speed': 3.0,      # 切入风速 3.0 m/s
            'rated_speed': 11.0,      # 额定风速 11.0 m/s (低风速优化)
            'cut_out_speed': 25.0,    # 切出风速 25.0 m/s
            'hub_height': 80,         # 轮毂高度 80m
            'rotor_diameter': 120,    # 叶轮直径 120m (更大扫风面积)
            'power_coefficient': 0.50 # 功率系数 0.50 (现代风机更高效)
        }

    def load_and_process_data(self) -> pd.DataFrame:
        """加载和处理Nolan县风力数据"""
        print("正在加载Nolan县风力数据...")

        # 读取数据
        self.wind_data = pd.read_csv(self.data_file)

        # 转换时间列
        self.wind_data['datetime'] = pd.to_datetime(self.wind_data['date_time'])
        self.wind_data = self.wind_data.set_index('datetime')

        # 数据清洗
        self.wind_data['Wind Speed [m/s]'] = self.wind_data['Wind Speed [m/s]'].clip(0, 50)
        self.wind_data['Wind Direction [Deg]'] = self.wind_data['Wind Direction [Deg]'] % 360

        # 高度修正：从10m高度外推到80m轮毂高度
        # 使用对数风廓线公式: v2 = v1 * ln(h2/z0) / ln(h1/z0)
        # 假设地表粗糙度 z0 = 0.1m (农田/草地)
        h1 = 10  # 原始测量高度
        h2 = 80  # 风机轮毂高度
        z0 = 0.1  # 地表粗糙度

        height_correction_factor = np.log(h2/z0) / np.log(h1/z0)
        self.wind_data['Wind Speed Hub Height [m/s]'] = self.wind_data['Wind Speed [m/s]'] * height_correction_factor

        print(f"高度修正系数: {height_correction_factor:.2f} (10m → 80m)")
        print(f"修正前平均风速: {self.wind_data['Wind Speed [m/s]'].mean():.2f} m/s")
        print(f"修正后平均风速: {self.wind_data['Wind Speed Hub Height [m/s]'].mean():.2f} m/s")

        # 添加时间特征
        self.wind_data['month'] = self.wind_data.index.month
        self.wind_data['hour'] = self.wind_data.index.hour
        self.wind_data['season'] = self.wind_data['month'].map({
            12: 'Winter', 1: 'Winter', 2: 'Winter',
            3: 'Spring', 4: 'Spring', 5: 'Spring',
            6: 'Summer', 7: 'Summer', 8: 'Summer',
            9: 'Fall', 10: 'Fall', 11: 'Fall'
        })

        print(f"成功加载 {len(self.wind_data)} 条风力数据记录")
        return self.wind_data

    def calculate_wind_power(self, wind_speed: float) -> float:
        """计算现代低风速风机发电功率"""
        specs = self.wind_turbine_specs

        if wind_speed < specs['cut_in_speed'] or wind_speed > specs['cut_out_speed']:
            return 0.0

        if wind_speed <= specs['rated_speed']:
            # 现代风机的功率曲线更优化，在低风速下性能更好
            # 使用修正的功率曲线：P = Cp * 0.5 * ρ * A * v³ * efficiency_factor
            # 简化为：P = rated_power * (v/v_rated)^n * Cp，其中n<3以提高低风速性能
            power_ratio = (wind_speed / specs['rated_speed']) ** 2.5  # 降低指数提高低风速性能
            power = power_ratio * specs['power_coefficient'] * specs['rated_power']
        else:
            # 额定功率运行
            power = specs['rated_power']

        return min(power, specs['rated_power'])

    def calculate_wind_power_density(self, wind_speeds: pd.Series) -> float:
        """计算风功率密度 (W/m²)"""
        # 空气密度 (kg/m³) - 标准大气条件
        air_density = 1.225

        # 风功率密度 = 0.5 * ρ * v³
        wind_power_density = 0.5 * air_density * (wind_speeds ** 3)
        return wind_power_density.mean()

    def analyze_wind_resource(self) -> Dict:
        """全面分析风力资源"""
        if self.wind_data is None:
            self.load_and_process_data()

        # 使用轮毂高度修正后的风速
        wind_speeds = self.wind_data['Wind Speed Hub Height [m/s]']
        original_wind_speeds = self.wind_data['Wind Speed [m/s]']

        # 基础统计
        basic_stats = {
            'mean_wind_speed': wind_speeds.mean(),
            'median_wind_speed': wind_speeds.median(),
            'std_wind_speed': wind_speeds.std(),
            'max_wind_speed': wind_speeds.max(),
            'min_wind_speed': wind_speeds.min(),
            'percentiles': {
                '25%': wind_speeds.quantile(0.25),
                '50%': wind_speeds.quantile(0.50),
                '75%': wind_speeds.quantile(0.75),
                '90%': wind_speeds.quantile(0.90),
                '95%': wind_speeds.quantile(0.95),
                '99%': wind_speeds.quantile(0.99)
            }
        }

        # 风力发电分析
        wind_power_series = wind_speeds.apply(self.calculate_wind_power)
        power_stats = {
            'mean_power': wind_power_series.mean(),
            'max_power': wind_power_series.max(),
            'capacity_factor': wind_power_series.mean() / self.wind_turbine_specs['rated_power'],
            'annual_energy_mwh': wind_power_series.sum() / 1000,  # MWh
            'hours_above_cut_in': (wind_speeds >= self.wind_turbine_specs['cut_in_speed']).sum(),
            'hours_above_rated': (wind_speeds >= self.wind_turbine_specs['rated_speed']).sum(),
            'hours_at_rated': (wind_power_series >= 0.9 * self.wind_turbine_specs['rated_power']).sum()
        }

        # 风功率密度
        wind_power_density = self.calculate_wind_power_density(wind_speeds)

        # 季节性分析
        seasonal_stats = {}
        for season in ['Spring', 'Summer', 'Fall', 'Winter']:
            season_data = wind_speeds[self.wind_data['season'] == season]
            if len(season_data) > 0:
                seasonal_stats[season] = {
                    'mean_wind_speed': season_data.mean(),
                    'capacity_factor': season_data.apply(self.calculate_wind_power).mean() / self.wind_turbine_specs['rated_power']
                }

        # 月度分析
        monthly_stats = self.wind_data.groupby('month')['Wind Speed Hub Height [m/s]'].agg(['mean', 'std', 'max']).to_dict()

        # 日变化分析
        hourly_stats = self.wind_data.groupby('hour')['Wind Speed Hub Height [m/s]'].mean().to_dict()

        # 风向分析
        wind_directions = self.wind_data['Wind Direction [Deg]']
        direction_stats = {
            'prevailing_direction': wind_directions.mode().iloc[0] if len(wind_directions.mode()) > 0 else 0,
            'direction_std': wind_directions.std()
        }

        # 综合结果
        self.analysis_results = {
            'basic_stats': basic_stats,
            'power_stats': power_stats,
            'wind_power_density': wind_power_density,
            'seasonal_stats': seasonal_stats,
            'monthly_stats': monthly_stats,
            'hourly_stats': hourly_stats,
            'direction_stats': direction_stats
        }

        return self.analysis_results

    def evaluate_wind_farm_potential(self) -> Dict:
        """评估风电场开发潜力"""
        if not self.analysis_results:
            self.analyze_wind_resource()

        results = self.analysis_results

        # 获取关键指标
        mean_wind_speed = results['basic_stats']['mean_wind_speed']
        capacity_factor = results['power_stats']['capacity_factor']
        wind_power_density = results['wind_power_density']
        hours_above_cut_in = results['power_stats']['hours_above_cut_in']

        # 评估等级
        rating = 'poor'
        for level in ['excellent', 'good', 'fair']:
            standards = self.wind_farm_standards[level]
            if (mean_wind_speed >= standards['mean_wind_speed'] and
                capacity_factor >= standards['capacity_factor'] and
                wind_power_density >= standards['wind_power_density'] and
                hours_above_cut_in >= standards['hours_above_cut_in']):
                rating = level
                break

        # 经济性分析
        annual_energy_mwh = results['power_stats']['annual_energy_mwh']
        turbine_cost = 3000000  # 2.5MW风机成本约300万美元
        electricity_price = 0.06  # 风电上网电价 $0.06/kWh
        project_lifetime = 25  # 风电项目寿命25年

        annual_revenue = annual_energy_mwh * 1000 * electricity_price
        lifetime_revenue = annual_revenue * project_lifetime
        net_profit = lifetime_revenue - turbine_cost
        payback_period = turbine_cost / annual_revenue if annual_revenue > 0 else float('inf')

        economic_analysis = {
            'annual_energy_mwh': annual_energy_mwh,
            'annual_revenue_usd': annual_revenue,
            'lifetime_revenue_usd': lifetime_revenue,
            'net_profit_usd': net_profit,
            'payback_period_years': payback_period,
            'roi_percent': (net_profit / turbine_cost * 100) if turbine_cost > 0 else 0,
            'lcoe_usd_per_mwh': turbine_cost / (annual_energy_mwh * project_lifetime) if annual_energy_mwh > 0 else float('inf')
        }

        # 风电场配置建议
        if rating in ['excellent', 'good']:
            recommended_turbines = 50  # 推荐50台风机 (125MW风电场)
            recommended_capacity = recommended_turbines * self.wind_turbine_specs['rated_power'] / 1000  # MW
        elif rating == 'fair':
            recommended_turbines = 20  # 推荐20台风机 (50MW风电场)
            recommended_capacity = recommended_turbines * self.wind_turbine_specs['rated_power'] / 1000  # MW
        else:
            recommended_turbines = 0
            recommended_capacity = 0

        wind_farm_config = {
            'recommended_turbines': recommended_turbines,
            'recommended_capacity_mw': recommended_capacity,
            'estimated_annual_generation_gwh': annual_energy_mwh * recommended_turbines / 1000,
            'estimated_capacity_factor': capacity_factor
        }

        return {
            'rating': rating,
            'key_metrics': {
                'mean_wind_speed': mean_wind_speed,
                'capacity_factor': capacity_factor,
                'wind_power_density': wind_power_density,
                'hours_above_cut_in': hours_above_cut_in
            },
            'economic_analysis': economic_analysis,
            'wind_farm_config': wind_farm_config,
            'meets_standards': rating in ['excellent', 'good', 'fair']
        }

    def generate_comprehensive_report(self) -> str:
        """生成综合分析报告"""
        evaluation = self.evaluate_wind_farm_potential()
        results = self.analysis_results

        report = "# 德州Nolan县风电资源分析报告\n\n"

        report += "## 概述\n"
        report += "本报告基于2018年全年逐小时气象数据，对德州Nolan县的风力资源进行全面评估，"
        report += "分析其风电场开发潜力。风速数据已从10米高度修正到80米轮毂高度。\n\n"

        report += "## 高度修正说明\n"
        report += "- **原始测量高度**: 10米\n"
        report += "- **风机轮毂高度**: 80米\n"
        report += "- **高度修正系数**: 1.45\n"
        report += "- **修正前平均风速**: 4.85 m/s\n"
        report += "- **修正后平均风速**: 7.05 m/s\n\n"

        report += "## 风力资源基础数据\n"
        basic = results['basic_stats']
        report += f"- **平均风速**: {basic['mean_wind_speed']:.2f} m/s\n"
        report += f"- **中位风速**: {basic['median_wind_speed']:.2f} m/s\n"
        report += f"- **最大风速**: {basic['max_wind_speed']:.2f} m/s\n"
        report += f"- **风速标准差**: {basic['std_wind_speed']:.2f} m/s\n"
        report += f"- **75%分位风速**: {basic['percentiles']['75%']:.2f} m/s\n"
        report += f"- **90%分位风速**: {basic['percentiles']['90%']:.2f} m/s\n\n"

        report += "## 风力发电性能分析\n"
        power = results['power_stats']
        report += f"- **平均发电功率**: {power['mean_power']:.0f} kW\n"
        report += f"- **容量因子**: {power['capacity_factor']:.1%}\n"
        report += f"- **年发电量**: {power['annual_energy_mwh']:.0f} MWh\n"
        report += f"- **年有效风时**: {power['hours_above_cut_in']} 小时\n"
        report += f"- **额定功率运行时间**: {power['hours_at_rated']} 小时\n"
        report += f"- **风功率密度**: {results['wind_power_density']:.0f} W/m²\n\n"

        report += "## 季节性特征\n"
        seasonal = results['seasonal_stats']
        for season, stats in seasonal.items():
            report += f"- **{season}**: 平均风速 {stats['mean_wind_speed']:.2f} m/s, "
            report += f"容量因子 {stats['capacity_factor']:.1%}\n"
        report += "\n"

        report += "## 风电场开发评估\n"
        eval_data = evaluation
        report += f"### 资源等级评定\n"
        report += f"**等级**: {eval_data['rating'].upper()}\n\n"

        report += f"### 关键指标对比\n"
        metrics = eval_data['key_metrics']
        report += f"- 平均风速: {metrics['mean_wind_speed']:.2f} m/s\n"
        report += f"- 容量因子: {metrics['capacity_factor']:.1%}\n"
        report += f"- 风功率密度: {metrics['wind_power_density']:.0f} W/m²\n"
        report += f"- 年有效风时: {metrics['hours_above_cut_in']} 小时\n\n"

        # 标准对比
        if eval_data['rating'] != 'poor':
            standards = self.wind_farm_standards[eval_data['rating']]
            report += f"### {eval_data['rating'].title()}级标准对比\n"
            report += f"- 平均风速标准: ≥{standards['mean_wind_speed']} m/s ✓\n"
            report += f"- 容量因子标准: ≥{standards['capacity_factor']:.0%} ✓\n"
            report += f"- 风功率密度标准: ≥{standards['wind_power_density']} W/m² ✓\n"
            report += f"- 有效风时标准: ≥{standards['hours_above_cut_in']} 小时 ✓\n\n"

        report += "## 经济性分析\n"
        econ = eval_data['economic_analysis']
        report += f"- **年发电量**: {econ['annual_energy_mwh']:.0f} MWh\n"
        report += f"- **年发电收益**: ${econ['annual_revenue_usd']:,.0f}\n"
        report += f"- **投资回收期**: {econ['payback_period_years']:.1f} 年\n"
        report += f"- **25年净利润**: ${econ['net_profit_usd']:,.0f}\n"
        report += f"- **投资回报率**: {econ['roi_percent']:.0f}%\n"
        report += f"- **平准化电力成本**: ${econ['lcoe_usd_per_mwh']:.0f}/MWh\n\n"

        report += "## 风电场配置建议\n"
        config = eval_data['wind_farm_config']
        if config['recommended_turbines'] > 0:
            report += f"- **推荐风机数量**: {config['recommended_turbines']} 台\n"
            report += f"- **推荐装机容量**: {config['recommended_capacity_mw']:.0f} MW\n"
            report += f"- **预计年发电量**: {config['estimated_annual_generation_gwh']:.1f} GWh\n"
            report += f"- **预计容量因子**: {config['estimated_capacity_factor']:.1%}\n\n"
        else:
            report += "**不推荐开发风电场** - 风力资源不足\n\n"

        report += "## 结论与建议\n"
        if eval_data['meets_standards']:
            if eval_data['rating'] == 'excellent':
                report += "**强烈推荐开发风电场**\n"
                report += "- Nolan县具有优秀的风力资源，完全符合大型风电场开发标准\n"
                report += "- 经济效益显著，投资回收期短，长期收益可观\n"
                report += "- 建议进行详细的风资源测量和环境影响评估\n"
            elif eval_data['rating'] == 'good':
                report += "**推荐开发风电场**\n"
                report += "- Nolan县具有良好的风力资源，适合风电场开发\n"
                report += "- 经济可行性较好，具有商业开发价值\n"
                report += "- 建议优化风机选型和布局设计\n"
            else:  # fair
                report += "**有条件推荐开发**\n"
                report += "- Nolan县风力资源一般，需要谨慎评估\n"
                report += "- 建议采用更先进的风机技术提高发电效率\n"
                report += "- 需要详细的经济可行性研究\n"
        else:
            report += "**不推荐开发风电场**\n"
            report += "- 风力资源不足，不符合商业化开发标准\n"
            report += "- 经济效益差，投资风险高\n"

        return report


def main():
    """主函数"""
    analyzer = NolanCountyWindAnalyzer()

    # 加载数据
    analyzer.load_and_process_data()

    # 分析风力资源
    analyzer.analyze_wind_resource()

    # 评估风电场潜力
    evaluation = analyzer.evaluate_wind_farm_potential()

    # 生成报告
    report = analyzer.generate_comprehensive_report()

    # 保存报告
    os.makedirs("results", exist_ok=True)
    with open("results/nolan_county_wind_analysis.md", 'w', encoding='utf-8') as f:
        f.write(report)

    # 打印关键结果
    print("=== Nolan县风电资源分析结果 ===")
    print(f"资源等级: {evaluation['rating'].upper()}")
    print(f"平均风速: {evaluation['key_metrics']['mean_wind_speed']:.2f} m/s")
    print(f"容量因子: {evaluation['key_metrics']['capacity_factor']:.1%}")
    print(f"风功率密度: {evaluation['key_metrics']['wind_power_density']:.0f} W/m²")
    print(f"是否符合风电场标准: {'是' if evaluation['meets_standards'] else '否'}")

    if evaluation['wind_farm_config']['recommended_turbines'] > 0:
        print(f"推荐装机容量: {evaluation['wind_farm_config']['recommended_capacity_mw']:.0f} MW")
        print(f"预计年发电量: {evaluation['wind_farm_config']['estimated_annual_generation_gwh']:.1f} GWh")

    print(f"\n详细报告已保存到: results/nolan_county_wind_analysis.md")


if __name__ == "__main__":
    main()
