{"random_seed": 102188, "root_directory": null, "central_agent": true, "simulation_start_time_step": 0, "simulation_end_time_step": 8759, "episode_time_steps": null, "rolling_episode_split": false, "random_episode_split": false, "seconds_per_time_step": 3600, "observations": {"month": {"active": true, "shared_in_central_agent": true}, "hour": {"active": true, "shared_in_central_agent": true}, "day_type": {"active": true, "shared_in_central_agent": true}, "daylight_savings_status": {"active": false, "shared_in_central_agent": true}, "outdoor_dry_bulb_temperature": {"active": true, "shared_in_central_agent": true}, "outdoor_relative_humidity": {"active": true, "shared_in_central_agent": true}, "diffuse_solar_irradiance": {"active": true, "shared_in_central_agent": true}, "direct_solar_irradiance": {"active": true, "shared_in_central_agent": true}, "outdoor_dry_bulb_temperature_predicted_1": {"active": true, "shared_in_central_agent": true}, "outdoor_dry_bulb_temperature_predicted_2": {"active": true, "shared_in_central_agent": true}, "outdoor_dry_bulb_temperature_predicted_3": {"active": true, "shared_in_central_agent": true}, "outdoor_relative_humidity_predicted_1": {"active": true, "shared_in_central_agent": true}, "outdoor_relative_humidity_predicted_2": {"active": true, "shared_in_central_agent": true}, "outdoor_relative_humidity_predicted_3": {"active": true, "shared_in_central_agent": true}, "diffuse_solar_irradiance_predicted_1": {"active": true, "shared_in_central_agent": true}, "diffuse_solar_irradiance_predicted_2": {"active": true, "shared_in_central_agent": true}, "diffuse_solar_irradiance_predicted_3": {"active": true, "shared_in_central_agent": true}, "direct_solar_irradiance_predicted_1": {"active": true, "shared_in_central_agent": true}, "direct_solar_irradiance_predicted_2": {"active": true, "shared_in_central_agent": true}, "direct_solar_irradiance_predicted_3": {"active": true, "shared_in_central_agent": true}, "electricity_pricing": {"active": false, "shared_in_central_agent": true}, "electricity_pricing_predicted_1": {"active": false, "shared_in_central_agent": true}, "electricity_pricing_predicted_2": {"active": false, "shared_in_central_agent": true}, "electricity_pricing_predicted_3": {"active": false, "shared_in_central_agent": true}, "carbon_intensity": {"active": false, "shared_in_central_agent": true}, "indoor_dry_bulb_temperature": {"active": true, "shared_in_central_agent": false}, "average_unmet_cooling_setpoint_difference": {"active": false, "shared_in_central_agent": false}, "indoor_relative_humidity": {"active": true, "shared_in_central_agent": false}, "non_shiftable_load": {"active": false, "shared_in_central_agent": false}, "dhw_demand": {"active": false, "shared_in_central_agent": false}, "cooling_demand": {"active": false, "shared_in_central_agent": false}, "heating_demand": {"active": false, "shared_in_central_agent": false}, "solar_generation": {"active": true, "shared_in_central_agent": false}, "occupant_count": {"active": false, "shared_in_central_agent": false}, "indoor_dry_bulb_temperature_cooling_set_point": {"active": true, "shared_in_central_agent": false}, "indoor_dry_bulb_temperature_heating_set_point": {"active": true, "shared_in_central_agent": false}, "comfort_band": {"active": true, "shared_in_central_agent": false}, "indoor_dry_bulb_temperature_cooling_delta": {"active": true, "shared_in_central_agent": false}, "indoor_dry_bulb_temperature_heating_delta": {"active": true, "shared_in_central_agent": false}, "hvac_mode": {"active": true, "shared_in_central_agent": false}, "power_outage": {"active": true, "shared_in_central_agent": false}, "cooling_storage_soc": {"active": false, "shared_in_central_agent": false}, "heating_storage_soc": {"active": false, "shared_in_central_agent": false}, "dhw_storage_soc": {"active": true, "shared_in_central_agent": false}, "electrical_storage_soc": {"active": true, "shared_in_central_agent": false}, "net_electricity_consumption": {"active": true, "shared_in_central_agent": false}, "cooling_electricity_consumption": {"active": true, "shared_in_central_agent": false}, "heating_electricity_consumption": {"active": true, "shared_in_central_agent": false}, "dhw_electricity_consumption": {"active": true, "shared_in_central_agent": false}, "cooling_storage_electricity_consumption": {"active": false, "shared_in_central_agent": false}, "heating_storage_electricity_consumption": {"active": false, "shared_in_central_agent": false}, "dhw_storage_electricity_consumption": {"active": true, "shared_in_central_agent": false}, "electrical_storage_electricity_consumption": {"active": true, "shared_in_central_agent": false}, "cooling_device_efficiency": {"active": false, "shared_in_central_agent": false}, "heating_device_efficiency": {"active": false, "shared_in_central_agent": false}, "dhw_device_efficiency": {"active": false, "shared_in_central_agent": false}}, "actions": {"cooling_storage": {"active": false}, "heating_storage": {"active": false}, "dhw_storage": {"active": true}, "electrical_storage": {"active": true}, "cooling_device": {"active": false}, "heating_device": {"active": false}, "cooling_or_heating_device": {"active": true}}, "agent": {"type": "citylearn.agents.base.BaselineAgent", "attributes": null}, "reward_function": {"type": "citylearn.reward_function.RewardFunction", "attributes": null}, "buildings": {"resstock-amy2018-2021-release-1-102188": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-102188.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 41.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 226.0184138789437}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-102188.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 7.190130429755277], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 27.110964913843564, 18.88394813789275, 1032.0, 487.5, 42.8, 6.0, 49.07125248499288], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-112223": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-112223.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 39.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 285.8468129046808}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-112223.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 9.377797365768014], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 19.58907128175249, 17.767208996790355, 1032.0, 487.5, 42.8, 3.0, 42.2378391600322], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-113808": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-113808.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 37.1, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 201.34958423912275}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-113808.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 9.753288513497406], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 19.27999202559822, 13.988271417985334, 1032.0, 487.5, 42.8, 3.0, 44.49851281360606], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-120912": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-120912.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 17.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 14.7}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 91.92384939469892}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-120912.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 2.141725288418934], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 12.384592525916087, 13.465404439351907, 1032.0, 487.5, 42.8, 1.0, 47.1125648147089], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-123182": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-123182.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 42.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 285.8468129046808}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-123182.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 3.59881330255172], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.32078294902389, 23.989628412366223, 1032.0, 487.5, 42.8, 2.0, 39.44285303899766], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-133435": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-133435.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 16.7, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.4}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 63.35994139069634}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-133435.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 1.6726733749831515], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 17.1799507488507, 14.523646617518216, 1032.0, 487.5, 42.8, 1.0, 43.67910525394661], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-133518": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-133518.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 35.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 24.2}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 168.215450954481}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-133518.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 0.8379581075161929], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 27.75889585166894, 35.62333308652721, 1032.0, 487.5, 42.8, 1.0, 42.74649128104882], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-134795": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-134795.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 19.1, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 14.8}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 102.72620005803094}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-134795.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 4.841141132281802], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 25.35750293144286, 21.01234681570812, 1032.0, 487.5, 42.8, 3.0, 43.53675357907933], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-136361": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-136361.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 21.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.3}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-136361.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 5.107869256759858], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.77477069938062, 13.273251198270328, 1032.0, 487.5, 42.8, 1.0, 47.7172614826788], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-147029": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-147029.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 20.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 18.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-147029.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 4.091466558751762], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.191414406622895, 12.673505511647033, 1032.0, 487.5, 42.8, 1.0, 43.42242880663818], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-153910": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-153910.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 26.3, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 18.9}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-153910.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 5.650120952305016], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 13.071796079383684, 16.118186100135244, 1032.0, 487.5, 42.8, 4.0, 47.18807353303405], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-155572": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-155572.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 22.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 18.1}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 116.83058590000027}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-155572.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 2.279619895559168], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.106127456958165, 22.43384157836546, 1032.0, 487.5, 42.8, 1.999999999999996, 43.31941936448954], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-15942": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-15942.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 40.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 285.8468129046808}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-15942.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 7.033385189177084], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 18.365926019348148, 12.871045732405982, 1032.0, 487.5, 42.8, 5.0, 43.18178038767482], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-166568": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-166568.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 22.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 18.2}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-166568.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 8.066283368695267], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 13.314398621069286, 11.873283873183214, 1032.0, 487.5, 42.8, 5.0, 47.26618066679868], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-171136": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-171136.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 36.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 255.93260925576897}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-171136.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 4.925980300212394], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 15.18811770859383, 12.145148101697414, 1032.0, 487.5, 42.8, 2.0, 41.5193948228201], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-179005": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-179005.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 34.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 235.3666019529799}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-179005.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 9.95429348549457], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 9.464939939941337, 10.113569169533502, 1032.0, 487.5, 42.8, 2.0, 45.590587372819165], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-192120": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-192120.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 24.7, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 15.2}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 87.76909913957142}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-192120.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 0.6286590414150646], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 30.56689708338834, 25.294739128816584, 1032.0, 487.5, 42.8, 3.0, 45.4612930639973], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-203716": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-203716.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 19.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 14.8}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-203716.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 7.594837707153523], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 9.844833403294064, 7.9822597660577905, 1032.0, 487.5, 42.8, 3.0, 42.27192355970234], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-20687": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-20687.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 33.7, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 235.36660195297992}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-20687.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 7.021659895960292], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 20.346158410301506, 13.346413985891392, 1032.0, 487.5, 42.8, 1.0, 44.99204933560553], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-208044": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-208044.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 34.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 201.34958423912275}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-208044.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 5.143197757711992], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 18.03644280096632, 28.571398515132326, 1032.0, 487.5, 42.8, 1.0, 42.25450324247401], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-213471": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-213471.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 34.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 235.36660195297992}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-213471.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 7.524920089254827], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.531549226563271, 10.058597769366587, 1032.0, 487.5, 42.8, 1.0, 44.3197425032436], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-21356": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-21356.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 38.6, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 214.09165178990432}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-21356.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 6.085324221508804], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 26.11903571364364, 28.31634931732872, 1032.0, 487.5, 42.8, 4.0, 41.20300718312397], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-216680": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-216680.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 16.3, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 14.7}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 91.92384939469892}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-216680.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 3.4450908142438417], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 12.049577076161665, 12.867222714366928, 1032.0, 487.5, 42.8, 1.0, 47.8646131612237], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-223548": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-223548.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 50.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 470.42159763682815}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-223548.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 10.800979351254105], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 36.35818058348122, 29.018287173328964, 1032.0, 487.5, 42.8, 4.0, 40.77660975548108], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-224341": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-224341.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 30.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 22.3}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 142.92340877639114}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-224341.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 1.952201133769826], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 30.051613290746253, 21.56803244323336, 1032.0, 487.5, 42.8, 4.0, 44.76571393436135], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-227400": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-227400.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 23.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 17.3}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 113.00920693947194}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-227400.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 4.24004635630391], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 13.722665363706932, 12.906185872941814, 1032.0, 487.5, 42.8, 4.0, 43.90568515823085], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-237652": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-237652.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 27.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 24.5}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 175.53819827914327}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-237652.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 6.068800195992095], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 13.565731798826512, 11.357948741063725, 1032.0, 487.5, 42.8, 1.0, 44.979824751429504], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-243797": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-243797.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 10.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 9.3}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 45.96192469734949}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-243797.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 5.527567530561088], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 10.441278135059832, 7.298908513035457, 1032.0, 487.5, 42.8, 1.0, 49.44190671821156], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-248224": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-248224.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 25.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 19.4}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 116.83058590000027}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-248224.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 4.905020227639902], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 16.705738605979175, 14.421936461876813, 1032.0, 487.5, 42.8, 3.000000000000004, 44.26382509734083], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-249373": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-249373.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 27.1, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 20.9}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 142.92340877639117}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-249373.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 3.730606282936704], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 28.04121000020073, 18.610594246591543, 1032.0, 487.5, 42.8, 1.0, 43.36465795801923], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-252982": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-252982.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 37.6, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 226.0184138789437}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-252982.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 1.3243824529382493], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 29.16177053878785, 21.155397790376178, 1032.0, 487.5, 42.8, 3.0, 43.17959236122411], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-258438": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-258438.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 25.7, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 18.1}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 113.00920693947194}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-258438.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 2.05730922457582], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 32.78118851792193, 26.01549165685552, 1032.0, 487.5, 42.8, 2.0, 46.57337776074765], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-264250": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-264250.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 23.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 17.7}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 116.83058590000026}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-264250.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 3.4427383221654644], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 18.255789027021155, 13.781166031309152, 1032.0, 487.5, 42.8, 1.999999999999996, 44.43990806405461], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-267773": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-267773.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 50.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 851.1005897628992}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-267773.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 12.08930838329222], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 48.8162659747108, 42.13067502398671, 1032.0, 487.5, 42.8, 5.0, 39.56571829967998], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-275175": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-275175.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 34.9, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 24.1}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 168.21545095448104}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-275175.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 1.7816052621033744], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 27.42445871930434, 34.806132963999815, 1032.0, 487.5, 42.8, 3.0, 44.22816070611941], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-276878": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-276878.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 38.3, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 276.6024982351226}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-276878.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 11.094139661585489], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.696977004221177, 11.852928040116192, 1032.0, 487.5, 42.8, 2.0, 42.558726334005634], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-280890": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-280890.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 33.3, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 235.36660195297992}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-280890.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 10.826089279106816], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 10.285453195259446, 9.64835358688439, 1032.0, 487.5, 42.8, 1.0, 48.327479788931534], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-281166": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-281166.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 20.9, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 17.4}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-281166.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 4.691852719175479], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.675996833543428, 14.016128238552287, 1032.0, 487.5, 42.8, 1.0, 44.59435400221601], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-283562": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-283562.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 26.6, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 18.4}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 142.92340877639114}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-283562.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 3.726704215894301], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 16.752508310295465, 16.612344114564582, 1032.0, 487.5, 42.8, 3.0, 40.94129619648135], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-284099": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-284099.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 32.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 24.5}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 226.01841387894368}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-284099.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 8.867040745058981], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.689291835933323, 12.459008469359798, 1032.0, 487.5, 42.8, 2.0, 41.93977642533319], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-290742": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-290742.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 47.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 336.43089587067084}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-290742.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 11.053974706761991], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 12.934065070872936, 10.047976942101489, 1032.0, 487.5, 42.8, 4.0, 42.29320049236259], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-294190": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-294190.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 41.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 285.8468129046808}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-294190.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 4.999478739402853], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 15.22702716685074, 13.390763827081368, 1032.0, 487.5, 42.8, 2.0, 43.59498425832838], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-294740": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-294740.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 24.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 17.4}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 156.63408802027698}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-294740.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 6.575469185741728], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 10.54247611147601, 9.221974330467706, 1032.0, 487.5, 42.8, 2.0, 44.299316456468894], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-296934": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-296934.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 23.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 19.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-296934.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 7.450312074922757], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 12.705146239416791, 12.984781411312763, 1032.0, 487.5, 42.8, 2.0, 50.00187296663741], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-297989": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-297989.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 20.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.2}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-297989.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 4.954106327961937], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 10.48797675366177, 9.444780848937585, 1032.0, 487.5, 42.8, 1.0, 48.89847488738374], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-304920": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-304920.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 30.7, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 22.8}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 168.215450954481}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-304920.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 2.6326523392408783], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 21.245571620553523, 30.756370474913204, 1032.0, 487.5, 42.8, 1.0, 40.98340296771155], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-305554": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-305554.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 38.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 285.8468129046808}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-305554.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 7.795972462635679], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 19.02881357886901, 19.43809090177797, 1032.0, 487.5, 42.8, 2.0, 41.43160709997292], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-307803": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-307803.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 45.1, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 336.4308958706709}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-307803.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 11.084698367364483], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 12.361598599170607, 11.799402608888718, 1032.0, 487.5, 42.8, 1.0, 42.13293148268107], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-315536": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-315536.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 18.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.9}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 121.83805123161768}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-315536.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 5.862733964681153], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 9.16826795054419, 8.63889186971588, 1032.0, 487.5, 42.8, 1.0, 48.08916035425139], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-321547": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-321547.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 32.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 226.01841387894368}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-321547.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 12.30677170703228], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.415064939878077, 8.267212982631047, 1032.0, 487.5, 42.8, 3.0, 44.64225905665306], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-338399": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-338399.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 21.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 15.7}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 113.00920693947194}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-338399.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 2.5207486402969312], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 18.33915069299584, 18.58388393150508, 1032.0, 487.5, 42.8, 2.0, 44.24980073179108], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-344233": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-344233.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 19.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.8}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-344233.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 7.287428803475839], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 10.251566365611415, 8.622003828874949, 1032.0, 487.5, 42.8, 1.0, 47.65222697374642], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-345731": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-345731.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 36.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 226.0184138789437}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-345731.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 3.563554145739894], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 21.48609700885305, 13.237444194214572, 1032.0, 487.5, 42.8, 2.0, 44.1408233436962], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-346066": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-346066.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 32.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 21.1}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 168.215450954481}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-346066.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 7.268518483762815], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 23.69954881191456, 19.24846899840625, 1032.0, 487.5, 42.8, 5.0, 44.16342259325644], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-358693": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-358693.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 25.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 23.6}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 175.53819827914327}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-358693.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 8.044246309213918], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.470854181314184, 14.438953181844074, 1032.0, 487.5, 42.8, 1.0, 42.42274810538766], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-36456": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-36456.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 20.6, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.6}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-36456.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 7.716309517553004], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.876281783009352, 10.609801924659074, 1032.0, 487.5, 42.8, 2.0, 46.08881750466455], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-373875": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-373875.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 22.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.5}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-373875.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 4.011906814767333], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 13.281654815686052, 12.199956643627573, 1032.0, 487.5, 42.8, 3.0, 44.24814341637489], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-378790": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-378790.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 37.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 235.36660195297992}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-378790.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 1.8434664928809728], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 16.755730328571136, 20.89476157743438, 1032.0, 487.5, 42.8, 1.0, 42.9465853168606], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-378943": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-378943.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 16.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 11.8}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 91.92384939469892}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-378943.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 8.64376897664664], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 9.113884720743124, 6.647628057773903, 1032.0, 487.5, 42.8, 2.0, 48.33920414481111], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-379248": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-379248.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 22.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 15.5}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 116.83058590000026}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-379248.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 3.763358624116377], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 19.074551450028466, 19.15516878232532, 1032.0, 487.5, 42.8, 3.000000000000004, 42.01811407243152], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-380135": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-380135.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 25.1, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 20.1}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 156.63408802027698}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-380135.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 4.099978174910131], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 15.156134927671776, 12.947689354530556, 1032.0, 487.5, 42.8, 1.0, 44.83359321892944], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-389891": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-389891.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 50.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 342.8707648044091}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-389891.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 7.003312842338854], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 18.221886612782185, 19.30536899813286, 1032.0, 487.5, 42.8, 2.0, 41.20509108265624], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-391687": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-391687.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 28.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 22.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 175.53819827914327}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-391687.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 7.350502392957642], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 16.66786304155584, 16.270717171282044, 1032.0, 487.5, 42.8, 3.0, 44.62030260327797], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-394712": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-394712.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 33.7, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 235.36660195297992}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-394712.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 10.04697556716378], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 8.916380252893257, 8.877928049013866, 1032.0, 487.5, 42.8, 1.0, 46.17949194993447], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-400743": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-400743.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 21.1, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 15.8}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-400743.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 3.332283149286431], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 9.752936903411262, 17.72654344921875, 1032.0, 487.5, 42.8, 1.0, 45.4694131857579], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-400973": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-400973.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 42.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 285.8468129046808}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-400973.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 10.795884290838192], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 19.01160258955157, 14.797355139899606, 1032.0, 487.5, 42.8, 4.0, 44.15594044488802], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-401821": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-401821.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 34.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 235.3666019529799}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-401821.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 9.090710084415589], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.638611644211723, 8.190614874856509, 1032.0, 487.5, 42.8, 1.0, 45.02189000162767], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-406573": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-406573.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 31.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 24.1}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 168.215450954481}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-406573.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 5.645993096168803], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 27.970233001343487, 26.61591076870681, 1032.0, 487.5, 42.8, 2.0, 43.27269497257915], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-412815": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-412815.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 19.9, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 15.4}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 116.83058590000027}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-412815.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 1.993405302695892], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 17.00848636620304, 22.16685180698962, 1032.0, 487.5, 42.8, 1.999999999999996, 40.06765458046857], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-419755": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-419755.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 24.6, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 18.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 156.63408802027698}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-419755.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 7.659287052350228], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 9.190529249977503, 6.61921078887395, 1032.0, 487.5, 42.8, 3.0, 45.47469075723054], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-427553": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-427553.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 36.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 255.93260925576894}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-427553.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 8.195380385401572], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 22.629275156735908, 16.674996671357157, 1032.0, 487.5, 42.8, 3.0, 42.10495764697514], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-43845": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-43845.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 33.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 22.7}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 168.215450954481}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-43845.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 3.2527084402839512], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 29.94956275671793, 21.336315269694687, 1032.0, 487.5, 42.8, 3.0, 45.04168550045946], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-449318": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-449318.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 20.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 17.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-449318.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 7.2504190639264126], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 9.24932060100391, 10.131501961315086, 1032.0, 487.5, 42.8, 1.0, 46.61253159090949], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-46421": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-46421.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 38.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 285.8468129046808}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-46421.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 8.269990888571966], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 15.446846917454064, 13.383039231328585, 1032.0, 487.5, 42.8, 2.0, 43.09678586933124], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-471221": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-471221.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 23.1, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 17.2}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 116.83058590000026}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-471221.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 2.07749355825341], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 15.486872759039482, 23.31709456928339, 1032.0, 487.5, 42.8, 1.0, 44.93613831515035], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-474782": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-474782.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 15.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 11.8}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 91.92384939469892}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-474782.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 9.899530518659112], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 5.286853689570374, 5.6802953644842775, 1032.0, 487.5, 42.8, 3.0, 46.52096766881456], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-479537": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-479537.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 33.1, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 235.36660195297992}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-479537.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 4.365495847984829], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 12.18153080816906, 10.3870290421355, 1032.0, 487.5, 42.8, 2.0, 43.457342857900166], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-484143": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-484143.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 36.1, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 235.36660195297992}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-484143.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 8.342637915622447], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 15.559617929521323, 10.684690778549328, 1032.0, 487.5, 42.8, 3.0, 46.86971620104576], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-486996": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-486996.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 21.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 22.9}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-486996.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 12.431612256239347], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 10.520297286068889, 5.497435966238305, 1032.0, 487.5, 42.8, 1.0, 50.127008566030185], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-494842": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-494842.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 50.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 910.9290166827107}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-494842.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 12.160937886715608], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 47.9199300294588, 77.15232761038801, 1032.0, 487.5, 42.8, 4.0, 37.31486759702493], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-50879": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-50879.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 41.9, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 285.8468129046808}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-50879.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 5.706568014061279], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 18.79323371204816, 21.28670454858944, 1032.0, 487.5, 42.8, 1.0, 48.79799531736754], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-511164": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-511164.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 41.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 201.34958423912275}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-511164.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 2.4894156715110043], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 44.74281790842281, 42.59438520654693, 1032.0, 487.5, 42.8, 3.0, 42.84691796493268], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-512011": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-512011.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 27.9, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 22.7}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 175.53819827914327}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-512011.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 9.10605872001802], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 9.603904513065777, 9.778842643835349, 1032.0, 487.5, 42.8, 4.0, 45.01270798385514], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-512927": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-512927.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 22.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 15.9}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 127.96630785793164}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-512927.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 3.8334585804369983], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 16.028765181680775, 14.776726329677295, 1032.0, 487.5, 42.8, 1.0, 43.14416007358808], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-519975": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-519975.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 50.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 402.6991605679938}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-519975.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 8.684206205109078], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 24.26350339866441, 24.60363184005193, 1032.0, 487.5, 42.8, 5.0, 40.30288930937728], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-523457": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-523457.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 34.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 23.8}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 205.45240500449012}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-523457.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 1.9991064851659448], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 17.664418566858117, 19.282949669145463, 1032.0, 487.5, 42.8, 1.0, 42.1257191758192], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-537787": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-537787.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 21.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 14.6}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 87.76909913957142}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-537787.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, -0.1863351417878437], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 17.36211646543, 28.01201105500865, 1032.0, 487.5, 42.8, 3.0, 41.31509608195543], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-537891": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-537891.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 16.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 15.7}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 87.76909913957142}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-537891.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 5.2098281914190325], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 17.767805613504432, 16.296197850742598, 1032.0, 487.5, 42.8, 1.0, 42.6683675407174], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-63192": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-63192.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 39.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 285.8468129046808}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-63192.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 7.086669408040758], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 19.84948995679729, 21.776110449574794, 1032.0, 487.5, 42.8, 5.0, 42.09372892350292], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-68142": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-68142.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 19.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.5}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-68142.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 7.562347556889323], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.374897160743483, 12.24268056447848, 1032.0, 487.5, 42.8, 2.0, 44.95328997201292], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-6887": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-6887.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 33.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 201.34958423912275}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-6887.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 7.592481724035105], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 22.8477480922708, 21.586821510972488, 1032.0, 487.5, 42.8, 1.999999999999996, 40.72094109925832], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-71553": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-71553.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 23.7, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 18.3}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-71553.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 9.035557809882953], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 12.18796501317008, 8.199077665575986, 1032.0, 487.5, 42.8, 2.0, 46.37909325750133], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-85296": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-85296.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 31.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 205.45240500449012}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-85296.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 9.081959270204692], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 17.675980632166816, 15.748886969949751, 1032.0, 487.5, 42.8, 2.0, 43.01695278568914], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-88708": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-88708.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 40.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 226.0184138789437}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-88708.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, -0.3595529124595967], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 28.09852743671816, 29.70573487253436, 1032.0, 487.5, 42.8, 2.0, 45.3493614476197], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-92392": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-92392.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 50.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 940.8431758319816}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-92392.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 9.72022261577697], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 33.472347194115386, 36.14775191382163, 1032.0, 487.5, 42.8, 3.0, 38.55802840172593], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-94184": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-94184.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 22.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.8}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-94184.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 0.3601583829211757], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.359149599109678, 13.378662643330191, 1032.0, 487.5, 42.8, 1.0, 45.38173028659943], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-96102": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-96102.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 50.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 470.4215976368281}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-96102.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 6.565822331723265], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 59.988835223809126, 32.83454892265561, 1032.0, 487.5, 42.8, 4.0, 41.33887055008849], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-97045": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-97045.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 44.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 336.43089587067084}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-97045.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 7.95926204291596], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 10.278611111385777, 14.312801993532617, 1032.0, 487.5, 42.8, 2.0, 39.487476950214514], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-97674": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-97674.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 15.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 10.9}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 63.35994139069634}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-97674.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 3.4617460706559413], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 13.291219558998034, 13.36505589180803, 1032.0, 487.5, 42.8, 3.0, 45.72428588185488], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-99758": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-99758.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 34.9, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 201.34958423912275}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-99758.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, -7.8, 0.0, 5.615982929640185], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 17.568741300518113, 16.993646424683245, 1032.0, 487.5, 42.8, 1.0, 41.86589982940668], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}}}