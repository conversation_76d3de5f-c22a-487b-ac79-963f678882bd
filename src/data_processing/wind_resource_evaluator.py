"""
风电资源评估器
评估各个数据集的风力资源潜力，决定是否配置风电
"""

import os
import json
from typing import Dict


class WindResourceEvaluator:
    """风电资源评估器"""

    def __init__(self):
        self.datasets = {
            'texas': 'dataset/tx_travis_county_neighborhood',
            'california': 'dataset/ca_alameda_county_neighborhood',
            'vermont': 'dataset/vt_chittenden_county_neighborhood'
        }
        self.evaluation_results = {}
        # 使用与Nolan县分析一致的现代风机标准
        self.wind_quality_thresholds = {
            'excellent': {'capacity_factor': 0.35, 'mean_wind_speed': 8.0, 'wind_power_density': 500},
            'good': {'capacity_factor': 0.25, 'mean_wind_speed': 6.5, 'wind_power_density': 350},
            'fair': {'capacity_factor': 0.18, 'mean_wind_speed': 5.5, 'wind_power_density': 250},
            'poor': {'capacity_factor': 0.0, 'mean_wind_speed': 0.0, 'wind_power_density': 0}
        }

        # 现代低风速风机参数 (与Nolan县分析一致)
        self.modern_turbine_specs = {
            'rated_power': 2500,      # 额定功率 2.5MW
            'cut_in_speed': 3.0,      # 切入风速 3.0 m/s
            'rated_speed': 11.0,      # 额定风速 11.0 m/s (低风速优化)
            'cut_out_speed': 25.0,    # 切出风速 25.0 m/s
            'hub_height': 80,         # 轮毂高度 80m
            'rotor_diameter': 120,    # 叶轮直径 120m
            'power_coefficient': 0.50 # 功率系数 0.50
        }

    def evaluate_all_datasets(self) -> Dict:
        """评估所有数据集的风力资源"""
        print("=== 开始风电资源评估 ===\n")

        for dataset_name, dataset_path in self.datasets.items():
            if not os.path.exists(dataset_path):
                print(f"警告: 数据集路径不存在 - {dataset_path}")
                continue

            epw_path = os.path.join(dataset_path, "weather.epw")
            if not os.path.exists(epw_path):
                print(f"警告: {dataset_name} 数据集缺少weather.epw文件")
                continue

            print(f"评估 {dataset_name} 数据集的风力资源...")
            try:
                # 使用现代风机参数进行分析
                analysis = self._analyze_wind_resource_modern(dataset_path)

                # 评估风力资源等级
                quality_rating = self._rate_wind_quality(analysis)

                # 计算经济性指标
                economic_metrics = self._calculate_economic_metrics(analysis)

                self.evaluation_results[dataset_name] = {
                    'dataset_path': dataset_path,
                    'wind_analysis': analysis,
                    'quality_rating': quality_rating,
                    'economic_metrics': economic_metrics,
                    'recommendation': self._generate_recommendation(quality_rating, economic_metrics)
                }

                print(f"✓ {dataset_name} 评估完成 - 风力资源等级: {quality_rating}")

            except Exception as e:
                print(f"✗ {dataset_name} 评估失败: {e}")
                self.evaluation_results[dataset_name] = {
                    'error': str(e),
                    'recommendation': 'no_wind'
                }

        return self.evaluation_results

    def _analyze_wind_resource_modern(self, dataset_path: str) -> Dict:
        """使用现代风机参数分析风力资源"""
        import pandas as pd
        import numpy as np

        # 读取EPW文件
        epw_path = os.path.join(dataset_path, "weather.epw")
        wind_data = []

        with open(epw_path, 'r') as f:
            lines = f.readlines()

        # 跳过EPW文件头部（前8行）
        data_lines = lines[8:]

        for line in data_lines:
            fields = line.strip().split(',')
            if len(fields) >= 22:
                try:
                    year = int(fields[0])
                    month = int(fields[1])
                    day = int(fields[2])
                    hour = int(fields[3])

                    # EPW文件中风向在索引20，风速在索引21
                    wind_direction = float(fields[20]) if len(fields) > 20 and fields[20] != '999' else 0.0
                    wind_speed = float(fields[21]) if len(fields) > 21 and fields[21] != '999' else 0.0

                    wind_data.append({
                        'year': year,
                        'month': month,
                        'day': day,
                        'hour': hour,
                        'wind_direction': wind_direction,
                        'wind_speed_10m': wind_speed
                    })

                except (ValueError, IndexError):
                    continue

        df = pd.DataFrame(wind_data)
        df['datetime'] = pd.to_datetime(df[['year', 'month', 'day', 'hour']])
        df = df.set_index('datetime')

        # 数据清洗
        df['wind_speed_10m'] = df['wind_speed_10m'].clip(0, 50)

        # 高度修正：从10m到80m
        height_correction_factor = np.log(80/0.1) / np.log(10/0.1)
        df['wind_speed_80m'] = df['wind_speed_10m'] * height_correction_factor

        # 添加时间特征
        df['month'] = df.index.month
        df['season'] = df['month'].map({
            12: 'Winter', 1: 'Winter', 2: 'Winter',
            3: 'Spring', 4: 'Spring', 5: 'Spring',
            6: 'Summer', 7: 'Summer', 8: 'Summer',
            9: 'Fall', 10: 'Fall', 11: 'Fall'
        })

        # 使用80m高度风速进行分析
        wind_speeds = df['wind_speed_80m']

        # 基础统计
        basic_stats = {
            'mean_wind_speed': wind_speeds.mean(),
            'median_wind_speed': wind_speeds.median(),
            'std_wind_speed': wind_speeds.std(),
            'max_wind_speed': wind_speeds.max(),
            'min_wind_speed': wind_speeds.min(),
            'percentiles': {
                '75%': wind_speeds.quantile(0.75),
                '90%': wind_speeds.quantile(0.90),
                '95%': wind_speeds.quantile(0.95)
            }
        }

        # 现代风机发电分析
        wind_power_series = wind_speeds.apply(self._calculate_modern_wind_power)
        power_stats = {
            'mean_power': wind_power_series.mean(),
            'max_power': wind_power_series.max(),
            'capacity_factor': wind_power_series.mean() / self.modern_turbine_specs['rated_power'],
            'annual_energy_mwh': wind_power_series.sum() / 1000,
            'hours_above_cut_in': (wind_speeds >= self.modern_turbine_specs['cut_in_speed']).sum(),
            'hours_above_rated': (wind_speeds >= self.modern_turbine_specs['rated_speed']).sum()
        }

        # 风功率密度
        air_density = 1.225
        wind_power_density = 0.5 * air_density * (wind_speeds ** 3)

        # 季节性分析
        seasonal_stats = {}
        for season in ['Spring', 'Summer', 'Fall', 'Winter']:
            season_data = wind_speeds[df['season'] == season]
            if len(season_data) > 0:
                season_power = season_data.apply(self._calculate_modern_wind_power)
                seasonal_stats[season] = {
                    'mean_wind_speed': season_data.mean(),
                    'capacity_factor': season_power.mean() / self.modern_turbine_specs['rated_power']
                }

        return {
            'basic_stats': basic_stats,
            'power_stats': power_stats,
            'wind_power_density': wind_power_density.mean(),
            'seasonal_stats': seasonal_stats,
            'height_correction_factor': height_correction_factor
        }

    def _calculate_modern_wind_power(self, wind_speed: float) -> float:
        """计算现代低风速风机发电功率"""
        specs = self.modern_turbine_specs

        if wind_speed < specs['cut_in_speed'] or wind_speed > specs['cut_out_speed']:
            return 0.0

        if wind_speed <= specs['rated_speed']:
            # 现代风机优化的功率曲线
            power_ratio = (wind_speed / specs['rated_speed']) ** 2.5
            power = power_ratio * specs['power_coefficient'] * specs['rated_power']
        else:
            # 额定功率运行
            power = specs['rated_power']

        return min(power, specs['rated_power'])

    def _rate_wind_quality(self, analysis: Dict) -> str:
        """评估风力资源质量等级"""
        capacity_factor = analysis['power_stats']['capacity_factor']
        mean_wind_speed = analysis['basic_stats']['mean_wind_speed']
        wind_power_density = analysis['wind_power_density']

        # 综合评估：容量因子、平均风速和风功率密度
        for level in ['excellent', 'good', 'fair']:
            thresholds = self.wind_quality_thresholds[level]
            if (capacity_factor >= thresholds['capacity_factor'] and
                mean_wind_speed >= thresholds['mean_wind_speed'] and
                wind_power_density >= thresholds['wind_power_density']):
                return level

        return 'poor'

    def _calculate_economic_metrics(self, analysis: Dict) -> Dict:
        """计算风电项目经济性指标"""
        power_stats = analysis['power_stats']

        # 现代2.5MW风机参数
        turbine_cost = 3000000  # 2.5MW风机成本 (USD)
        electricity_price = 0.06  # 风电上网电价 (USD/kWh)
        project_lifetime = 25  # 项目寿命 (年)

        annual_energy = power_stats['annual_energy_mwh'] * 1000  # 转换为kWh
        annual_revenue = annual_energy * electricity_price

        # 简单投资回收期
        payback_period = turbine_cost / annual_revenue if annual_revenue > 0 else float('inf')

        # 生命周期收益
        lifetime_revenue = annual_revenue * project_lifetime
        net_profit = lifetime_revenue - turbine_cost

        return {
            'annual_energy_kwh': annual_energy,
            'annual_revenue_usd': annual_revenue,
            'payback_period_years': payback_period,
            'lifetime_revenue_usd': lifetime_revenue,
            'net_profit_usd': net_profit,
            'roi_percent': (net_profit / turbine_cost * 100) if turbine_cost > 0 else 0
        }

    def _generate_recommendation(self, quality_rating: str, economic_metrics: Dict) -> str:
        """生成风电配置建议"""
        payback_period = economic_metrics['payback_period_years']
        roi = economic_metrics['roi_percent']

        if quality_rating == 'excellent' and payback_period <= 10 and roi > 100:
            return 'highly_recommended'
        elif quality_rating in ['excellent', 'good'] and payback_period <= 15 and roi > 50:
            return 'recommended'
        elif quality_rating in ['good', 'fair'] and payback_period <= 20 and roi > 0:
            return 'conditional'
        else:
            return 'not_recommended'

    def generate_comprehensive_report(self) -> str:
        """生成综合评估报告"""
        report = "# 风电资源综合评估报告\n\n"

        report += "## 评估概述\n"
        report += "本报告评估了三个数据集区域的风力资源潜力，为VPP风电配置提供决策依据。\n\n"

        report += "## 评估标准\n"
        report += "### 风力资源等级标准 (基于现代2.5MW风机，80m轮毂高度)\n"
        report += f"- **优秀 (Excellent)**: 容量因子 ≥ {self.wind_quality_thresholds['excellent']['capacity_factor']:.0%}, 平均风速 ≥ {self.wind_quality_thresholds['excellent']['mean_wind_speed']:.1f} m/s, 风功率密度 ≥ {self.wind_quality_thresholds['excellent']['wind_power_density']:.0f} W/m²\n"
        report += f"- **良好 (Good)**: 容量因子 ≥ {self.wind_quality_thresholds['good']['capacity_factor']:.0%}, 平均风速 ≥ {self.wind_quality_thresholds['good']['mean_wind_speed']:.1f} m/s, 风功率密度 ≥ {self.wind_quality_thresholds['good']['wind_power_density']:.0f} W/m²\n"
        report += f"- **一般 (Fair)**: 容量因子 ≥ {self.wind_quality_thresholds['fair']['capacity_factor']:.0%}, 平均风速 ≥ {self.wind_quality_thresholds['fair']['mean_wind_speed']:.1f} m/s, 风功率密度 ≥ {self.wind_quality_thresholds['fair']['wind_power_density']:.0f} W/m²\n"
        report += f"- **较差 (Poor)**: 不满足以上任何标准\n\n"

        report += "### 经济性评估标准\n"
        report += "- **强烈推荐**: 投资回收期 ≤ 10年, ROI > 100%\n"
        report += "- **推荐**: 投资回收期 ≤ 15年, ROI > 50%\n"
        report += "- **有条件推荐**: 投资回收期 ≤ 20年, ROI > 0%\n"
        report += "- **不推荐**: 投资回收期 > 20年, ROI ≤ 0%\n\n"

        report += "## 各区域评估结果\n\n"

        for dataset_name, result in self.evaluation_results.items():
            if 'error' in result:
                report += f"### {dataset_name.title()} 区域\n"
                report += f"**状态**: 评估失败 - {result['error']}\n"
                report += f"**建议**: 不配置风电\n\n"
                continue

            analysis = result['wind_analysis']
            quality = result['quality_rating']
            economics = result['economic_metrics']
            recommendation = result['recommendation']

            report += f"### {dataset_name.title()} 区域\n"

            # 风力资源指标
            report += "#### 风力资源指标 (80m轮毂高度)\n"
            report += f"- **平均风速**: {analysis['basic_stats']['mean_wind_speed']:.2f} m/s\n"
            report += f"- **最大风速**: {analysis['basic_stats']['max_wind_speed']:.2f} m/s\n"
            report += f"- **风速标准差**: {analysis['basic_stats']['std_wind_speed']:.2f} m/s\n"
            report += f"- **容量因子**: {analysis['power_stats']['capacity_factor']:.1%}\n"
            report += f"- **年发电量**: {analysis['power_stats']['annual_energy_mwh']:.0f} MWh (单台2.5MW风机)\n"
            report += f"- **风功率密度**: {analysis['wind_power_density']:.0f} W/m²\n"
            report += f"- **高度修正系数**: {analysis['height_correction_factor']:.2f} (10m→80m)\n"
            report += f"- **资源等级**: {quality.upper()}\n\n"

            # 经济性指标
            report += "#### 经济性指标\n"
            report += f"- **年发电收益**: ${economics['annual_revenue_usd']:.0f}\n"
            report += f"- **投资回收期**: {economics['payback_period_years']:.1f} 年\n"
            report += f"- **投资回报率**: {economics['roi_percent']:.1f}%\n"
            report += f"- **20年净利润**: ${economics['net_profit_usd']:.0f}\n\n"

            # 配置建议
            report += "#### 配置建议\n"
            recommendation_text = {
                'highly_recommended': '**强烈推荐配置风电** - 风力资源优秀，经济效益显著',
                'recommended': '**推荐配置风电** - 风力资源良好，具有经济可行性',
                'conditional': '**有条件推荐** - 风力资源一般，需谨慎评估项目风险',
                'not_recommended': '**不推荐配置风电** - 风力资源较差，经济效益不佳'
            }
            report += recommendation_text.get(recommendation, '未知建议') + "\n\n"

        # 总结建议
        report += "## 总结与建议\n\n"

        recommended_datasets = []
        not_recommended_datasets = []

        for dataset_name, result in self.evaluation_results.items():
            if 'error' in result or result['recommendation'] == 'not_recommended':
                not_recommended_datasets.append(dataset_name)
            elif result['recommendation'] in ['highly_recommended', 'recommended']:
                recommended_datasets.append(dataset_name)

        if recommended_datasets:
            report += f"### 推荐配置风电的区域\n"
            for dataset in recommended_datasets:
                report += f"- **{dataset.title()}**: 适合风电开发\n"
            report += "\n"

        if not_recommended_datasets:
            report += f"### 不推荐配置风电的区域\n"
            for dataset in not_recommended_datasets:
                report += f"- **{dataset.title()}**: 风力资源不足或经济性较差\n"
            report += "\n"

        report += "### VPP配置策略建议\n"
        report += "1. **优先发展**: 在风力资源优秀的区域配置风电，提高VPP的可再生能源比例\n"
        report += "2. **因地制宜**: 根据各区域的资源禀赋，采用不同的能源配置策略\n"
        report += "3. **经济优化**: 重点关注投资回收期和长期收益，确保项目可持续性\n"
        report += "4. **技术选型**: 根据风速分布特征选择合适的风机类型和容量\n\n"

        return report

    def save_results(self, output_dir: str = "results"):
        """保存评估结果"""
        os.makedirs(output_dir, exist_ok=True)

        # 保存详细评估数据
        results_file = os.path.join(output_dir, "wind_resource_evaluation.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            # 转换numpy类型为Python原生类型以便JSON序列化
            serializable_results = self._make_json_serializable(self.evaluation_results)
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)

        # 保存综合报告
        report = self.generate_comprehensive_report()
        report_file = os.path.join(output_dir, "wind_resource_evaluation_report.md")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"风电资源评估结果已保存到: {output_dir}")
        return results_file, report_file

    def _make_json_serializable(self, obj):
        """将numpy类型转换为JSON可序列化的类型"""
        import numpy as np

        if isinstance(obj, dict):
            return {key: self._make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return obj


def main():
    """主函数"""
    evaluator = WindResourceEvaluator()

    # 评估所有数据集
    results = evaluator.evaluate_all_datasets()

    # 保存结果
    evaluator.save_results()

    # 打印简要结果
    print("\n=== 风电资源评估结果摘要 ===")
    for dataset_name, result in results.items():
        if 'error' in result:
            print(f"{dataset_name}: 评估失败")
        else:
            quality = result['quality_rating']
            recommendation = result['recommendation']
            capacity_factor = result['wind_analysis']['power_stats']['capacity_factor']
            print(f"{dataset_name}: {quality} (容量因子: {capacity_factor:.1%}) - {recommendation}")


if __name__ == "__main__":
    main()
