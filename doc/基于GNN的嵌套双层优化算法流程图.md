# 基于GNN的嵌套双层优化算法流程图

## 1. 总体算法流程图

```mermaid
flowchart TD
    A[开始] --> B["输入: 电网需求P_ref,L(t), VPP图结构数据, 历史运行数据"]
    B --> C["Step 1: 图状态更新与特征提取"]
    C --> D["Step 2: 大型VPP层GNN推理"]
    D --> E["Step 3: 中型VPP层GNN推理 并行执行"]
    E --> F["Step 4: 小型VPP层GNN推理 并行执行"]
    F --> G["Step 5: 双层一致性检验与反馈"]
    G --> H{收敛检查}
    H -->|未收敛| I[在线微调GNN参数]
    I --> D
    H -->|收敛| J["Step 6: 实时约束验证与输出"]
    J --> K[生成控制指令]
    K --> L[更新GNN模型参数]
    L --> M[结束]

    style A fill:#e1f5fe
    style M fill:#e8f5e8
    style H fill:#fff3e0
    style I fill:#fce4ec
```

## 2. 详细分步流程图

### Step 1: 图状态更新与特征提取

```mermaid
flowchart TD
    A[Step 1: 图状态更新与特征提取] --> B[1.1: 更新VPP层次图结构]
    B --> C[构建大型VPP图 G_L]
    B --> D[构建中型VPP图 G_M]
    B --> E[构建小型VPP图 G_S]

    C --> F[1.2: 提取节点特征矩阵]
    D --> F
    E --> F

    F --> G[大型VPP特征 X_L<br/>- 总装机容量<br/>- 可再生能源占比<br/>- 储能容量<br/>- 负荷预测时序<br/>- 市场价格<br/>- 电网连接状态]

    F --> H[中型VPP特征 X_M<br/>- 光伏发电时序<br/>- 风电发电时序<br/>- 负荷需求时序<br/>- 储能SOC时序<br/>- EV可用数量时序<br/>- 协调调节能力]

    F --> I[小型VPP特征 X_S<br/>- 建筑类型<br/>- 光伏装机容量<br/>- 可控负荷时序<br/>- EV充电时序<br/>- 热惯性参数<br/>- 舒适温度范围]

    G --> J[1.3: 构建时序特征序列]
    H --> J
    I --> J

    J --> K["H_temporal(t-T:t)<br/>历史T个时间步的特征序列"]
    K --> L[输出: 图结构G, 特征矩阵X, 时序特征H]

    style A fill:#e3f2fd
    style L fill:#e8f5e8
```

### Step 2: 大型VPP层GNN推理

```mermaid
flowchart TD
    A[Step 2: 大型VPP层GNN推理] --> B[2.1: 输入处理]
    B --> C[输入大型VPP图 G_L]
    B --> D["输入电网需求 P_ref,L(t)"]
    B --> E[输入下层反馈 medium_responses]

    C --> F[2.2: GNN-Large前向推理]
    D --> F
    E --> F

    F --> G[VPP感知图卷积层<br/>- 功率流约束感知<br/>- 功率平衡约束嵌入<br/>- 经济信号传播<br/>- 多约束融合]

    G --> H[时序感知图注意力<br/>- 时序注意力机制<br/>- 空间注意力机制<br/>- 时空融合]

    H --> I[多层GNN堆叠<br/>Layer 1 → Layer 2 → ... → Layer L]

    I --> J[2.3: 输出处理]
    J --> K["中型VPP功率分配<br/>P_ref,m(t) for each m"]
    J --> L["响应能力预测<br/>P_capability,m(t)"]
    J --> M["不确定性评估<br/>σ_uncertainty,m(t)"]

    K --> N[输出: large_decisions]
    L --> N
    M --> N

    style A fill:#e3f2fd
    style N fill:#e8f5e8
```

### Step 3: 中型VPP层GNN推理（并行执行）

```mermaid
flowchart TD
    A[Step 3: 中型VPP层GNN推理] --> B[For each 中型VPP m ∈ M_L]
    B --> C[3.1: 接收上层GNN输出]
    C --> D["P_ref,m(t) from GNN-Large"]

    D --> E[3.2: 更新图节点约束]
    E --> F["G_m.ndata['upper_constraint'] = large_decisions[m]"]

    F --> G[3.3: GNN-Medium推理]
    G --> H[VPP感知图卷积层<br/>- 区域功率平衡<br/>- 储能协调约束<br/>- 小型VPP协调信号]

    H --> I[时序感知图注意力<br/>- 区域内时序协调<br/>- 小型VPP空间关联<br/>- 负荷-发电匹配]

    I --> J[多层GNN处理<br/>考虑上层约束的优化]

    J --> K["输出小型VPP功率分配<br/>P_ref,s(t) for each s ∈ S_m"]

    K --> L[并行处理所有中型VPP]
    L --> M{所有中型VPP<br/>处理完成?}
    M -->|否| B
    M -->|是| N[输出: medium_decisions]

    style A fill:#e3f2fd
    style N fill:#e8f5e8
    style L fill:#fff3e0
```

### Step 4: 小型VPP层GNN推理（并行执行）

```mermaid
flowchart TD
    A[Step 4: 小型VPP层GNN推理] --> B[For each 小型VPP s ∈ S_m]
    B --> C[4.1: 接收上层GNN输出]
    C --> D["P_ref,s(t) from GNN-Medium"]

    D --> E[4.2: GNN-Small推理]
    E --> F[建筑级图卷积<br/>- 设备级功率约束<br/>- 用户舒适度约束<br/>- 经济效益优化]

    F --> G[设备控制策略生成<br/>- 储能充放电策略<br/>- 可调负荷控制<br/>- EV充电调度<br/>- HVAC温度控制]

    G --> H[4.3: 实际输出能力预测]
    H --> I["考虑设备约束的<br/>实际功率输出<br/>P_capability,s(t)"]

    I --> J["考虑不确定性的<br/>响应能力评估<br/>σ_response,s(t)"]

    J --> K[并行处理所有小型VPP]
    K --> L{所有小型VPP<br/>处理完成?}
    L -->|否| B
    L -->|是| M[输出: small_responses]

    style A fill:#e3f2fd
    style M fill:#e8f5e8
    style K fill:#fff3e0
```

### Step 5: 双层一致性检验与反馈

```mermaid
flowchart TD
    A[Step 5: 双层一致性检验与反馈] --> B[5.1: 聚合小型VPP实际输出]
    B --> C["∑ P_capability,s(t) → P_actual,m(t)"]

    C --> D[5.2: 聚合中型VPP实际输出]
    D --> E["∑ P_actual,m(t) → P_actual,L(t)"]

    E --> F[5.3: 计算双层一致性损失]
    F --> G["L_consistency = ||P_ref - P_actual||²"]
    F --> H[L_bilevel = KKT_condition_loss + complementary_slackness_loss]

    G --> I[5.4: 收敛判断]
    H --> I
    I --> J{L_consistency > ε_threshold?}

    J -->|是| K[在线微调GNN参数]
    K --> L[梯度计算<br/>∇θ L_total]
    L --> M[参数更新<br/>θ ← θ - η∇θ L_total]
    M --> N[返回Step 2重新推理]

    J -->|否| O[收敛确认]
    O --> P[输出: 收敛的优化解]

    style A fill:#e3f2fd
    style P fill:#e8f5e8
    style J fill:#fff3e0
    style K fill:#fce4ec
```

### Step 6: 实时约束验证与输出

```mermaid
flowchart TD
    A[Step 6: 实时约束验证与输出] --> B[6.1: 验证物理约束可行性]
    B --> C[功率平衡约束检查<br/>∑P_gen = ∑P_load + P_grid]
    B --> D[设备容量约束检查<br/>P_min ≤ P_device ≤ P_max]
    B --> E["爬坡率约束检查<br/>|dP/dt| ≤ R_max"]

    C --> F[6.2: 生成标准化控制指令]
    D --> F
    E --> F

    F --> G[大型VPP控制指令<br/>- 电网交互功率<br/>- 市场交易计划<br/>- 中型VPP协调信号]

    F --> H[中型VPP控制指令<br/>- 区域功率分配<br/>- 储能协调控制<br/>- 小型VPP调度指令]

    F --> I[小型VPP控制指令<br/>- 设备开关控制<br/>- 功率设定值<br/>- 温度设定点<br/>- 充电策略]

    G --> J[6.3: 更新GNN模型参数]
    H --> J
    I --> J

    J --> K[在线学习<br/>基于实际运行数据<br/>持续优化模型]

    K --> L[输出最终控制指令]

    style A fill:#e3f2fd
    style L fill:#e8f5e8
```

## 3. GNN网络架构流程图

```mermaid
flowchart TD
    A["输入图数据 G(V,E,X)"] --> B[VPP感知图卷积层]
    B --> C["功率流约束感知消息传递<br/>m_ij = MLP([h_i ‖ h_j ‖ e_ij])"]
    C --> D["功率平衡约束嵌入<br/>c_i = Constraint_Embedding(P_balance_i)"]
    D --> E["经济信号传播<br/>e_i = Economic_Signal(price_i, cost_i)"]
    E --> F["多约束融合<br/>h_i' = Fusion(m_ij, c_i, e_i)"]

    F --> G[时序感知图注意力层]
    G --> H["时序注意力计算<br/>α_t = Attention(h_t, h_t-1:t-T)"]
    H --> I["空间注意力计算<br/>α_ij = Attention(h_i, h_j)"]
    I --> J["时空融合<br/>h_final = TimeSpace_Fusion(α_t, α_ij)"]

    J --> K[输出层]
    K --> L["功率分配决策<br/>P_ref = MLP(h_final)"]
    K --> M["不确定性估计<br/>σ = Uncertainty_Head(h_final)"]
    K --> N["置信度评分<br/>confidence = Confidence_Head(h_final)"]

    style A fill:#e1f5fe
    style L fill:#e8f5e8
    style M fill:#e8f5e8
    style N fill:#e8f5e8
```

## 4. 训练流程图

```mermaid
flowchart TD
    A[开始训练] --> B["Phase 1: 单层GNN预训练"]
    B --> C["训练GNN-Small<br/>目标: 建筑级优化"]
    B --> D["训练GNN-Medium<br/>目标: 区域级协调"]
    B --> E["训练GNN-Large<br/>目标: 电网级稳定"]

    C --> F["Phase 2: 双层GNN训练"]
    D --> F
    E --> F

    F --> G["训练Large-Medium双层<br/>固定Small层参数"]
    F --> H["训练Medium-Small双层<br/>固定Large层参数"]

    G --> I["Phase 3: 嵌套三层联合训练"]
    H --> I

    I --> J["端到端联合优化<br/>所有层级同时训练"]
    J --> K["多目标损失函数<br/>L_total = L_large + L_medium + L_small + L_consistency"]

    K --> L{收敛检查}
    L -->|未收敛| M["调整学习率<br/>继续训练"]
    M --> J
    L -->|收敛| N["Phase 4: 在线自适应微调"]

    N --> O[部署到实际系统]
    O --> P["基于实时数据<br/>持续学习优化"]
    P --> Q[训练完成]

    style A fill:#e1f5fe
    style Q fill:#e8f5e8
    style L fill:#fff3e0
```

## 5. 实时运行流程图

```mermaid
flowchart TD
    A[实时运行开始] --> B["接收当前状态数据<br/>current_state"]
    B --> C["预测时间窗口<br/>forecast_horizon=24h"]

    C --> D["调用GNN求解器<br/>nested_bilevel_gnn_solver.solve"]
    D --> E["图状态更新<br/>update_graph_states"]
    E --> F["GNN推理求解<br/>三层嵌套双层优化"]

    F --> G["解的可行性验证<br/>constraint_validator.verify"]
    G --> H{"约束满足?"}

    H -->|否| I["约束修正<br/>调整解的可行性"]
    I --> G

    H -->|是| J["控制指令转换<br/>solution_translator.to_control_signals"]
    J --> K["下发控制指令<br/>到各层级VPP"]

    K --> L[执行控制动作]
    L --> M["收集执行反馈<br/>actual_performance"]

    M --> N["在线学习更新<br/>model.online_update"]
    N --> O["等待下一个控制周期<br/>通常5-15分钟"]

    O --> B

    style A fill:#e1f5fe
    style H fill:#fff3e0
    style I fill:#fce4ec
```

这些流程图详细展示了基于GNN的嵌套双层优化算法的完整执行过程，从数据输入到最终控制指令输出的每一个步骤都有清晰的描述。
