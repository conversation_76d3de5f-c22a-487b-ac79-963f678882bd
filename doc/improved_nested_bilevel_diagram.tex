\documentclass{beamer}
\usepackage{ctex, hyperref}
\usepackage[T1]{fontenc}

% other packages from template
\usepackage{latexsym,amsmath,xcolor,multicol,booktabs,calligra}
\usepackage{graphicx,pstricks,listings,stackengine}
\usepackage{amssymb} % For symbols like \mathbb

% --- TikZ for diagrams ---
\usepackage{tikz}
\usetikzlibrary{shapes.geometric, arrows, positioning, fit, backgrounds, calc, decorations.pathreplacing}

% Use the Northeastern theme from the provided template
\usepackage{Northeastern}

% 设置英文/数字字体 (通过替换默认的 sans-serif 字体)
\setsansfont{TeX Gyre Termes}

\begin{document}

\kaishu
\begin{frame}
    \frametitle{嵌套双层优化的VPP层级结构}

\begin{tikzpicture}[
    scale=0.7, transform shape,
    node distance=0.8cm and 1cm,
    % 定义节点样式
    level1/.style={rectangle, draw=red!80, fill=red!10, minimum width=5.5cm, minimum height=2cm, align=center, font=\small\bfseries},
    level2_dual/.style={rectangle, draw=orange!80, fill=orange!10, minimum width=5cm, minimum height=3cm, align=center, font=\small\bfseries},
    level3/.style={rectangle, draw=blue!80, fill=blue!10, minimum width=4.5cm, minimum height=2cm, align=center, font=\small},
    nested_box/.style={rectangle, draw=purple!60, fill=purple!5, rounded corners=8pt, thick, dashed},
    % 箭头样式
    leader_arrow/.style={-latex', thick, red!70, line width=1.5pt},
    follower_arrow/.style={-latex', thick, blue!70, line width=1.2pt},
    feedback_arrow/.style={-latex', thick, green!60, line width=1pt, dashed}
]

% 大型VPP层 (纯Leader角色)
\node[level1] (L-VPP) {
    \textcolor{red!80}{\textbf{大型VPP层}} \\
    \textbf{角色:} Leader \\
    (领导者) \\
    \textbf{目标:} 功率稳定导向
};

% 中型VPP层 (双重角色)
\node[level2_dual, below=of L-VPP] (M-VPP) {
    \textcolor{orange!80}{\textbf{中型VPP层}} \\
    \textbf{双重角色:} \\
    \textcolor{blue!70}{Follower} \textbf{+} \textcolor{red!70}{Leader} \\
    \textcolor{blue!70}{• 作为Follower:} \\
    \textcolor{blue!70}{接受大型VPP功率指令} \\
    \textcolor{red!70}{• 作为Leader:} \\
    \textcolor{red!70}{向小型VPP下达协调指令} \\
    \textbf{目标:} 协调平衡导向
};

% 小型VPP层 (纯Follower角色)
\node[level3, below=of M-VPP] (S-VPP) {
    \textcolor{blue!80}{\textbf{小型VPP层}} \\
    \textbf{角色:} Follower \\
    (跟随者) \\
    \textbf{目标:} 经济效益/ \\
    用户舒适度导向
};

% 领导-跟随箭头 (下达指令)
\draw[leader_arrow] (L-VPP.south) -- (M-VPP.north)
    node[midway, right=0.3cm, font=\footnotesize] {
        \textcolor{red!70}{\textbf{下达指令}} \\
        功率指令 $P_{ref,m}$ \\
        协调价格 $\lambda_{price}$
    };

\draw[leader_arrow] (M-VPP.south) -- (S-VPP.north)
    node[midway, right=0.3cm, font=\footnotesize] {
        \textcolor{red!70}{\textbf{下达指令}} \\
        功率指令 $P_{ref,s}$ \\
        调度策略
    };

% 跟随者反馈箭头 (能力上报)
\draw[feedback_arrow] (M-VPP.west) -- ++(-2,0) |- (L-VPP.west)
    node[midway, left=0.1cm, font=\footnotesize] {
        \textcolor{green!60}{\textbf{能力反馈}} \\
        聚合能力 $P_{cap,m}$ \\
        不确定性 $\sigma_m$
    };

\draw[feedback_arrow] (S-VPP.west) -- ++(-2.5,0) |- (M-VPP.west)
    node[midway, left=0.1cm, font=\footnotesize] {
        \textcolor{green!60}{\textbf{能力反馈}} \\
        响应能力 $P_{cap,s}$ \\
        成本信息 $C_s$
    };

% 嵌套结构背景框
\begin{pgfonlayer}{background}
    \node[nested_box, fit=(L-VPP) (M-VPP), inner sep=0.6cm] (nest1) {};
    \node[nested_box, fit=(M-VPP) (S-VPP), inner sep=0.6cm] (nest2) {};
\end{pgfonlayer}

% 嵌套标签
\node[above=0.2cm of nest1, font=\small\bfseries, color=purple!80] {
    \textbf{嵌套双层优化-1:} 大型VPP主导的功率稳定控制
};
\node[below=0.2cm of nest2, font=\small\bfseries, color=purple!80] {
    \textbf{嵌套双层优化-2:} 中型VPP主导的区域协调控制
};

% 添加决策层次说明
\node[right=1.5cm of L-VPP, font=\scriptsize, align=left] (decision_flow) {
    \textbf{决策流程:} \\
    1. 电网调度中心下达总体需求 \\
    2. 大型VPP分解为中型VPP指令 \\
    3. 中型VPP进一步分解为小型VPP指令 \\
    4. 小型VPP执行并反馈实际能力 \\
    5. 逐层聚合形成整体响应 \\[0.3cm]
    \textbf{嵌套双层特点:} \\
    • 中型VPP具有双重身份 \\
    • 上层关注全局稳定性 \\
    • 下层优化局部经济性 \\
    • 形成完整决策链条
};

% 连接决策流程说明
\draw[dotted, gray] (L-VPP.east) -- (decision_flow.west);

\end{tikzpicture}
\end{frame}
\end{document}
