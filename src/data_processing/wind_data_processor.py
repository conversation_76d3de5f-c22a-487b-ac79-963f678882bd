"""
风电数据处理模块
从佛蒙特州数据集提取风力数据，为CityLearn数据集补充风电模拟
"""

import pandas as pd
import numpy as np
import os
from typing import Dict, List, Tuple, Optional
from pathlib import Path
import matplotlib.pyplot as plt


class WindDataProcessor:
    """风电数据处理器"""

    def __init__(self, dataset_path: str = None):
        self.dataset_path = dataset_path
        self.wind_data = None
        self.wind_power_curve = self._create_wind_power_curve()
        self.dataset_name = self._get_dataset_name(dataset_path) if dataset_path else "unknown"

    def _get_dataset_name(self, dataset_path: str) -> str:
        """从数据集路径提取数据集名称"""
        if not dataset_path:
            return "unknown"

        path_parts = dataset_path.split('/')
        if 'tx_travis_county' in dataset_path:
            return "texas"
        elif 'ca_alameda_county' in dataset_path:
            return "california"
        elif 'vt_chittenden_county' in dataset_path:
            return "vermont"
        elif 'citylearn' in dataset_path:
            return "citylearn"
        else:
            return path_parts[-1] if path_parts else "unknown"

    def _create_wind_power_curve(self) -> Dict:
        """创建风力发电功率曲线"""
        # 基于典型小型风机的功率曲线 (例如: 10kW风机)
        return {
            'cut_in_speed': 3.0,      # 切入风速 m/s
            'rated_speed': 12.0,      # 额定风速 m/s
            'cut_out_speed': 25.0,    # 切出风速 m/s
            'rated_power': 10.0,      # 额定功率 kW
            'power_coefficient': 0.35  # 功率系数
        }

    def extract_wind_data_from_epw(self) -> pd.DataFrame:
        """从EPW文件提取风力数据"""
        if not self.dataset_path:
            raise ValueError("数据集路径未设置")

        epw_path = os.path.join(self.dataset_path, "weather.epw")

        if not os.path.exists(epw_path):
            raise FileNotFoundError(f"EPW文件不存在: {epw_path}")

        wind_data = []

        with open(epw_path, 'r') as f:
            lines = f.readlines()

        # 跳过EPW文件头部（前8行）
        data_lines = lines[8:]

        for line in data_lines:
            fields = line.strip().split(',')
            if len(fields) >= 22:  # 确保有足够的字段
                try:
                    year = int(fields[0])
                    month = int(fields[1])
                    day = int(fields[2])
                    hour = int(fields[3])

                    # EPW文件中风向在第21列（索引20），风速在第22列（索引21）
                    wind_direction = float(fields[20]) if fields[20] != '999' else 0.0
                    wind_speed = float(fields[21]) if fields[21] != '999' else 0.0

                    # 其他气象数据
                    dry_bulb_temp = float(fields[6]) if fields[6] != '99.9' else 20.0
                    humidity = float(fields[8]) if fields[8] != '999' else 50.0

                    wind_data.append({
                        'year': year,
                        'month': month,
                        'day': day,
                        'hour': hour,
                        'wind_direction': wind_direction,
                        'wind_speed': wind_speed,
                        'dry_bulb_temperature': dry_bulb_temp,
                        'relative_humidity': humidity
                    })

                except (ValueError, IndexError) as e:
                    print(f"解析EPW数据行时出错: {e}")
                    continue

        df = pd.DataFrame(wind_data)

        # 创建时间索引
        df['datetime'] = pd.to_datetime(df[['year', 'month', 'day', 'hour']])
        df = df.set_index('datetime')

        # 数据清洗
        df['wind_speed'] = df['wind_speed'].clip(0, 50)  # 限制风速范围
        df['wind_direction'] = df['wind_direction'] % 360  # 确保风向在0-360度

        self.wind_data = df
        return df

    def calculate_wind_power(self, wind_speed: float, turbine_capacity: float = 10.0) -> float:
        """
        根据风速计算风力发电功率

        Args:
            wind_speed: 风速 (m/s)
            turbine_capacity: 风机容量 (kW)

        Returns:
            发电功率 (kW)
        """
        curve = self.wind_power_curve

        if wind_speed < curve['cut_in_speed'] or wind_speed > curve['cut_out_speed']:
            return 0.0

        if wind_speed <= curve['rated_speed']:
            # 三次方关系（简化模型）
            power_ratio = (wind_speed / curve['rated_speed']) ** 3
            power = power_ratio * curve['power_coefficient'] * turbine_capacity
        else:
            # 额定功率
            power = curve['power_coefficient'] * turbine_capacity

        return min(power, turbine_capacity)

    def generate_wind_power_series(self, turbine_capacity: float = 10.0) -> pd.Series:
        """生成风力发电功率时间序列"""
        if self.wind_data is None:
            self.extract_wind_data_from_epw()

        wind_power = self.wind_data['wind_speed'].apply(
            lambda x: self.calculate_wind_power(x, turbine_capacity)
        )

        return wind_power

    def create_citylearn_weather_with_wind(self, output_path: str = None) -> pd.DataFrame:
        """
        为CityLearn数据集创建包含风力数据的天气文件

        Args:
            output_path: 输出文件路径

        Returns:
            包含风力数据的天气DataFrame
        """
        # 加载CityLearn原始天气数据
        citylearn_weather_path = "dataset/citylearn_challenge_2022_phase_all_plus_evs/weather.csv"
        citylearn_weather = pd.read_csv(citylearn_weather_path)

        # 提取风力数据
        if self.wind_data is None:
            self.extract_wind_data_from_epw()

        # 确保数据长度匹配
        wind_data_subset = self.wind_data.head(len(citylearn_weather)).copy()

        # 添加风力数据到CityLearn天气数据
        enhanced_weather = citylearn_weather.copy()
        enhanced_weather['wind_speed'] = wind_data_subset['wind_speed'].values
        enhanced_weather['wind_direction'] = wind_data_subset['wind_direction'].values

        # 计算风力发电功率（假设10kW风机）
        enhanced_weather['wind_power_10kw'] = enhanced_weather['wind_speed'].apply(
            lambda x: self.calculate_wind_power(x, 10.0)
        )

        # 保存增强的天气数据
        if output_path:
            enhanced_weather.to_csv(output_path, index=False)
            print(f"增强的天气数据已保存到: {output_path}")

        return enhanced_weather

    def analyze_wind_resource(self) -> Dict:
        """分析风力资源特征"""
        if self.wind_data is None:
            self.extract_wind_data_from_epw()

        wind_speeds = self.wind_data['wind_speed']

        analysis = {
            'mean_wind_speed': wind_speeds.mean(),
            'median_wind_speed': wind_speeds.median(),
            'std_wind_speed': wind_speeds.std(),
            'max_wind_speed': wind_speeds.max(),
            'min_wind_speed': wind_speeds.min(),
            'wind_speed_percentiles': {
                '25%': wind_speeds.quantile(0.25),
                '75%': wind_speeds.quantile(0.75),
                '90%': wind_speeds.quantile(0.90),
                '95%': wind_speeds.quantile(0.95)
            }
        }

        # 计算风力发电潜力
        wind_power_10kw = self.generate_wind_power_series(10.0)
        analysis['wind_power_stats'] = {
            'mean_power': wind_power_10kw.mean(),
            'max_power': wind_power_10kw.max(),
            'capacity_factor': wind_power_10kw.mean() / 10.0,
            'hours_above_rated': (wind_power_10kw >= 3.5).sum(),  # 35%额定功率以上
            'total_energy_kwh': wind_power_10kw.sum()
        }

        # 按月分析
        monthly_stats = self.wind_data.groupby('month')['wind_speed'].agg(['mean', 'std', 'max'])
        analysis['monthly_wind_stats'] = monthly_stats.to_dict()

        return analysis

    def plot_wind_analysis(self, save_path: str = None) -> None:
        """绘制风力分析图表"""
        if self.wind_data is None:
            self.extract_wind_data_from_epw()

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 风速分布直方图
        axes[0, 0].hist(self.wind_data['wind_speed'], bins=50, alpha=0.7, edgecolor='black')
        axes[0, 0].set_xlabel('风速 (m/s)')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].set_title('风速分布')
        axes[0, 0].grid(True, alpha=0.3)

        # 风速时间序列（前30天）
        wind_subset = self.wind_data.head(24*30)
        axes[0, 1].plot(wind_subset.index, wind_subset['wind_speed'])
        axes[0, 1].set_xlabel('时间')
        axes[0, 1].set_ylabel('风速 (m/s)')
        axes[0, 1].set_title('风速时间序列（前30天）')
        axes[0, 1].grid(True, alpha=0.3)

        # 风力发电功率
        wind_power = self.generate_wind_power_series(10.0)
        wind_power_subset = wind_power.head(24*30)
        axes[1, 0].plot(wind_power_subset.index, wind_power_subset.values)
        axes[1, 0].set_xlabel('时间')
        axes[1, 0].set_ylabel('发电功率 (kW)')
        axes[1, 0].set_title('风力发电功率（10kW风机，前30天）')
        axes[1, 0].grid(True, alpha=0.3)

        # 月平均风速
        monthly_wind = self.wind_data.groupby('month')['wind_speed'].mean()
        axes[1, 1].bar(monthly_wind.index, monthly_wind.values)
        axes[1, 1].set_xlabel('月份')
        axes[1, 1].set_ylabel('平均风速 (m/s)')
        axes[1, 1].set_title('月平均风速')
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"风力分析图表已保存到: {save_path}")

        plt.show()

    def generate_wind_report(self) -> str:
        """生成风力资源分析报告"""
        analysis = self.analyze_wind_resource()

        report = "=== 佛蒙特州奇滕登县风力资源分析报告 ===\n\n"

        report += "风速统计:\n"
        report += f"- 平均风速: {analysis['mean_wind_speed']:.2f} m/s\n"
        report += f"- 中位风速: {analysis['median_wind_speed']:.2f} m/s\n"
        report += f"- 风速标准差: {analysis['std_wind_speed']:.2f} m/s\n"
        report += f"- 最大风速: {analysis['max_wind_speed']:.2f} m/s\n"
        report += f"- 75%分位风速: {analysis['wind_speed_percentiles']['75%']:.2f} m/s\n\n"

        report += "风力发电潜力 (10kW风机):\n"
        power_stats = analysis['wind_power_stats']
        report += f"- 平均发电功率: {power_stats['mean_power']:.2f} kW\n"
        report += f"- 容量因子: {power_stats['capacity_factor']:.1%}\n"
        report += f"- 年发电量: {power_stats['total_energy_kwh']:.0f} kWh\n"
        report += f"- 有效发电小时数: {power_stats['hours_above_rated']} 小时\n\n"

        report += "适用性评估:\n"
        if power_stats['capacity_factor'] > 0.25:
            report += "- 风力资源优秀，适合风电开发\n"
        elif power_stats['capacity_factor'] > 0.15:
            report += "- 风力资源良好，可考虑风电开发\n"
        else:
            report += "- 风力资源一般，需谨慎评估风电项目\n"

        return report


def main():
    """主函数 - 演示风电数据处理"""
    processor = WindDataProcessor()

    # 提取风力数据
    print("正在提取佛蒙特州风力数据...")
    wind_data = processor.extract_wind_data_from_epw()
    print(f"成功提取 {len(wind_data)} 条风力数据记录")

    # 生成分析报告
    report = processor.generate_wind_report()
    print(report)

    # 为CityLearn创建增强的天气数据
    print("\n正在为CityLearn数据集创建增强天气数据...")
    enhanced_weather = processor.create_citylearn_weather_with_wind(
        "dataset/citylearn_challenge_2022_phase_all_plus_evs/weather_with_wind.csv"
    )

    # 生成分析图表
    processor.plot_wind_analysis("results/wind_analysis.png")


if __name__ == "__main__":
    main()
