"""
加州风力资源高度对比分析
分析不同轮毂高度对加州地区风力资源等级的影响
"""

import numpy as np


def height_correction(h1, h2, z0=0.1):
    """计算高度修正系数"""
    return np.log(h2/z0) / np.log(h1/z0)


def calc_modern_power(v):
    """计算现代风机功率"""
    if v < 3.0 or v > 25.0:
        return 0
    elif v <= 11.0:
        return 2500 * (v/11.0)**2.5 * 0.5
    else:
        return 2500


def analyze_california_heights():
    """分析加州不同高度的风力资源"""
    
    # 加州原始10m风速数据
    california_wind_10m = 5.37
    
    print("=== 加州风力资源高度对比分析 ===")
    print(f"原始10m风速: {california_wind_10m:.2f} m/s")
    
    # 风力资源等级标准
    standards = {
        'excellent': {'capacity_factor': 0.35, 'mean_wind_speed': 8.0, 'wind_power_density': 500},
        'good': {'capacity_factor': 0.25, 'mean_wind_speed': 6.5, 'wind_power_density': 350},
        'fair': {'capacity_factor': 0.18, 'mean_wind_speed': 5.5, 'wind_power_density': 250}
    }
    
    # 不同高度的分析
    heights = [80, 100, 120, 140]
    results = {}
    
    for height in heights:
        correction_factor = height_correction(10, height)
        wind_speed = california_wind_10m * correction_factor
        power = calc_modern_power(wind_speed)
        capacity_factor = power / 2500
        annual_energy = power * 8760 / 1000  # MWh
        
        # 风功率密度计算
        air_density = 1.225
        wind_power_density = 0.5 * air_density * (wind_speed ** 3)
        
        # 评估等级
        level = 'poor'
        for grade in ['excellent', 'good', 'fair']:
            std = standards[grade]
            if (capacity_factor >= std['capacity_factor'] and
                wind_speed >= std['mean_wind_speed'] and
                wind_power_density >= std['wind_power_density']):
                level = grade
                break
        
        results[height] = {
            'correction_factor': correction_factor,
            'wind_speed': wind_speed,
            'power': power,
            'capacity_factor': capacity_factor,
            'annual_energy': annual_energy,
            'wind_power_density': wind_power_density,
            'level': level
        }
        
        print(f"\n{height}m轮毂高度:")
        print(f"  修正系数: {correction_factor:.2f}")
        print(f"  风速: {wind_speed:.2f} m/s")
        print(f"  平均功率: {power:.0f} kW")
        print(f"  容量因子: {capacity_factor:.1%}")
        print(f"  年发电量: {annual_energy:.0f} MWh")
        print(f"  风功率密度: {wind_power_density:.0f} W/m²")
        print(f"  资源等级: {level.upper()}")
        
        # 检查各等级标准
        for grade, std in standards.items():
            cf_ok = capacity_factor >= std['capacity_factor']
            ws_ok = wind_speed >= std['mean_wind_speed']
            wpd_ok = wind_power_density >= std['wind_power_density']
            all_ok = cf_ok and ws_ok and wpd_ok
            
            print(f"  满足{grade}标准: {all_ok} (容量因子:{cf_ok}, 风速:{ws_ok}, 密度:{wpd_ok})")
    
    return results


def economic_analysis_100m():
    """100m高度的详细经济分析"""
    print("\n=== 经济性分析 (100m高度) ===")
    
    # 计算100m高度参数
    california_wind_10m = 5.37
    height_100m = height_correction(10, 100)
    wind_100m = california_wind_10m * height_100m
    power_100m = calc_modern_power(wind_100m)
    capacity_factor_100m = power_100m / 2500
    annual_energy_100m = power_100m * 8760 / 1000
    
    # 经济参数
    turbine_cost_100m = 3500000  # 100m轮毂高度风机成本更高
    electricity_price = 0.06
    project_lifetime = 25
    
    annual_revenue = annual_energy_100m * 1000 * electricity_price
    lifetime_revenue = annual_revenue * project_lifetime
    net_profit = lifetime_revenue - turbine_cost_100m
    payback_period = turbine_cost_100m / annual_revenue if annual_revenue > 0 else float('inf')
    roi = (net_profit / turbine_cost_100m * 100) if turbine_cost_100m > 0 else 0
    
    print(f"年发电收益: ${annual_revenue:,.0f}")
    print(f"投资回收期: {payback_period:.1f}年")
    print(f"25年净利润: ${net_profit:,.0f}")
    print(f"投资回报率: {roi:.0f}%")
    
    # 与80m高度对比
    print("\n=== 与80m高度对比 ===")
    height_80m = height_correction(10, 80)
    wind_80m = california_wind_10m * height_80m
    power_80m = calc_modern_power(wind_80m)
    annual_energy_80m = power_80m * 8760 / 1000
    
    turbine_cost_80m = 3000000  # 80m轮毂高度风机成本
    annual_revenue_80m = annual_energy_80m * 1000 * electricity_price
    payback_period_80m = turbine_cost_80m / annual_revenue_80m
    
    print(f"80m高度:")
    print(f"  年发电量: {annual_energy_80m:.0f} MWh")
    print(f"  年收益: ${annual_revenue_80m:,.0f}")
    print(f"  投资回收期: {payback_period_80m:.1f}年")
    
    print(f"100m高度:")
    print(f"  年发电量: {annual_energy_100m:.0f} MWh")
    print(f"  年收益: ${annual_revenue:,.0f}")
    print(f"  投资回收期: {payback_period:.1f}年")
    
    print(f"提升效果:")
    print(f"  发电量提升: {(annual_energy_100m/annual_energy_80m-1)*100:.1f}%")
    print(f"  收益提升: {(annual_revenue/annual_revenue_80m-1)*100:.1f}%")
    print(f"  回收期缩短: {payback_period_80m-payback_period:.1f}年")


def generate_recommendation():
    """生成配置建议"""
    print("\n=== VPP配置建议 ===")
    
    # 分析结果
    results = analyze_california_heights()
    
    print("\n基于分析结果的建议:")
    
    # 检查100m高度是否达到良好标准
    result_100m = results[100]
    if result_100m['level'] == 'good':
        print("✅ 100m轮毂高度风机可以达到'良好'等级")
        print("✅ 建议在加州VPP项目中配置风电")
        print("✅ 推荐配置: 10-20MW风电 + 储能 + 光伏")
    elif result_100m['level'] == 'fair':
        print("⚠️  100m轮毂高度风机达到'一般'等级")
        print("⚠️  可以考虑小规模风电配置")
        print("⚠️  推荐配置: 5-10MW风电 + 储能 + 光伏")
    else:
        print("❌ 即使100m高度仍不满足风电配置标准")
        print("❌ 建议专注于储能 + 光伏配置")
    
    # 技术建议
    print(f"\n技术参数建议:")
    print(f"- 轮毂高度: 100m (容量因子{result_100m['capacity_factor']:.1%})")
    print(f"- 风机类型: 2.5MW低风速风机")
    print(f"- 预期年发电量: {result_100m['annual_energy']:.0f} MWh/台")
    print(f"- 投资回收期: 约8-9年")


def main():
    """主函数"""
    results = analyze_california_heights()
    economic_analysis_100m()
    generate_recommendation()
    
    # 保存结果到文件
    with open("results/california_height_analysis.txt", 'w', encoding='utf-8') as f:
        f.write("加州风力资源高度对比分析结果\n")
        f.write("="*50 + "\n\n")
        
        for height, result in results.items():
            f.write(f"{height}m轮毂高度:\n")
            f.write(f"  风速: {result['wind_speed']:.2f} m/s\n")
            f.write(f"  容量因子: {result['capacity_factor']:.1%}\n")
            f.write(f"  年发电量: {result['annual_energy']:.0f} MWh\n")
            f.write(f"  风功率密度: {result['wind_power_density']:.0f} W/m²\n")
            f.write(f"  资源等级: {result['level'].upper()}\n\n")
    
    print(f"\n详细结果已保存到: results/california_height_analysis.txt")


if __name__ == "__main__":
    main()
