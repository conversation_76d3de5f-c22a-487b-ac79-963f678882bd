{"random_seed": 102040, "root_directory": null, "central_agent": true, "simulation_start_time_step": 0, "simulation_end_time_step": 8759, "episode_time_steps": null, "rolling_episode_split": false, "random_episode_split": false, "seconds_per_time_step": 3600, "observations": {"month": {"active": true, "shared_in_central_agent": true}, "hour": {"active": true, "shared_in_central_agent": true}, "day_type": {"active": true, "shared_in_central_agent": true}, "daylight_savings_status": {"active": false, "shared_in_central_agent": true}, "outdoor_dry_bulb_temperature": {"active": true, "shared_in_central_agent": true}, "outdoor_relative_humidity": {"active": true, "shared_in_central_agent": true}, "diffuse_solar_irradiance": {"active": true, "shared_in_central_agent": true}, "direct_solar_irradiance": {"active": true, "shared_in_central_agent": true}, "outdoor_dry_bulb_temperature_predicted_1": {"active": true, "shared_in_central_agent": true}, "outdoor_dry_bulb_temperature_predicted_2": {"active": true, "shared_in_central_agent": true}, "outdoor_dry_bulb_temperature_predicted_3": {"active": true, "shared_in_central_agent": true}, "outdoor_relative_humidity_predicted_1": {"active": true, "shared_in_central_agent": true}, "outdoor_relative_humidity_predicted_2": {"active": true, "shared_in_central_agent": true}, "outdoor_relative_humidity_predicted_3": {"active": true, "shared_in_central_agent": true}, "diffuse_solar_irradiance_predicted_1": {"active": true, "shared_in_central_agent": true}, "diffuse_solar_irradiance_predicted_2": {"active": true, "shared_in_central_agent": true}, "diffuse_solar_irradiance_predicted_3": {"active": true, "shared_in_central_agent": true}, "direct_solar_irradiance_predicted_1": {"active": true, "shared_in_central_agent": true}, "direct_solar_irradiance_predicted_2": {"active": true, "shared_in_central_agent": true}, "direct_solar_irradiance_predicted_3": {"active": true, "shared_in_central_agent": true}, "electricity_pricing": {"active": false, "shared_in_central_agent": true}, "electricity_pricing_predicted_1": {"active": false, "shared_in_central_agent": true}, "electricity_pricing_predicted_2": {"active": false, "shared_in_central_agent": true}, "electricity_pricing_predicted_3": {"active": false, "shared_in_central_agent": true}, "carbon_intensity": {"active": false, "shared_in_central_agent": true}, "indoor_dry_bulb_temperature": {"active": true, "shared_in_central_agent": false}, "average_unmet_cooling_setpoint_difference": {"active": false, "shared_in_central_agent": false}, "indoor_relative_humidity": {"active": true, "shared_in_central_agent": false}, "non_shiftable_load": {"active": false, "shared_in_central_agent": false}, "dhw_demand": {"active": false, "shared_in_central_agent": false}, "cooling_demand": {"active": false, "shared_in_central_agent": false}, "heating_demand": {"active": false, "shared_in_central_agent": false}, "solar_generation": {"active": true, "shared_in_central_agent": false}, "occupant_count": {"active": false, "shared_in_central_agent": false}, "indoor_dry_bulb_temperature_cooling_set_point": {"active": true, "shared_in_central_agent": false}, "indoor_dry_bulb_temperature_heating_set_point": {"active": true, "shared_in_central_agent": false}, "comfort_band": {"active": true, "shared_in_central_agent": false}, "indoor_dry_bulb_temperature_cooling_delta": {"active": true, "shared_in_central_agent": false}, "indoor_dry_bulb_temperature_heating_delta": {"active": true, "shared_in_central_agent": false}, "hvac_mode": {"active": true, "shared_in_central_agent": false}, "power_outage": {"active": true, "shared_in_central_agent": false}, "cooling_storage_soc": {"active": false, "shared_in_central_agent": false}, "heating_storage_soc": {"active": false, "shared_in_central_agent": false}, "dhw_storage_soc": {"active": true, "shared_in_central_agent": false}, "electrical_storage_soc": {"active": true, "shared_in_central_agent": false}, "net_electricity_consumption": {"active": true, "shared_in_central_agent": false}, "cooling_electricity_consumption": {"active": true, "shared_in_central_agent": false}, "heating_electricity_consumption": {"active": true, "shared_in_central_agent": false}, "dhw_electricity_consumption": {"active": true, "shared_in_central_agent": false}, "cooling_storage_electricity_consumption": {"active": false, "shared_in_central_agent": false}, "heating_storage_electricity_consumption": {"active": false, "shared_in_central_agent": false}, "dhw_storage_electricity_consumption": {"active": true, "shared_in_central_agent": false}, "electrical_storage_electricity_consumption": {"active": true, "shared_in_central_agent": false}, "cooling_device_efficiency": {"active": false, "shared_in_central_agent": false}, "heating_device_efficiency": {"active": false, "shared_in_central_agent": false}, "dhw_device_efficiency": {"active": false, "shared_in_central_agent": false}}, "actions": {"cooling_storage": {"active": false}, "heating_storage": {"active": false}, "dhw_storage": {"active": true}, "electrical_storage": {"active": true}, "cooling_device": {"active": false}, "heating_device": {"active": false}, "cooling_or_heating_device": {"active": true}}, "agent": {"type": "citylearn.agents.base.BaselineAgent", "attributes": null}, "reward_function": {"type": "citylearn.reward_function.RewardFunction", "attributes": null}, "buildings": {"resstock-amy2018-2021-release-1-102040": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-102040.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 25.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 17.1}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 113.00920693947204}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-102040.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 8.263237554872054], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 21.15806369653833, 26.33786999296823, 1007.5, 465.5, 28.9, 3.0, 34.3472898111364], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-103125": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-103125.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 18.3, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 18.8}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 87.76909913957142}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-103125.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 8.32720721114022], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 12.117373054663574, 7.917690651434842, 1007.5, 465.5, 28.9, 2.0, 31.84087091349276], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-103603": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-103603.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 42.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 226.01841387894368}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-103603.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 8.667054706594083], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 24.62548208678106, 20.79418555826589, 1007.5, 465.5, 28.9, 3.0, 34.59693098975456], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-11487": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-11487.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 19.3, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 17.3}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-11487.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.985616341377138], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.34004676494031, 11.160575122455985, 1007.5, 465.5, 28.9, 1.0, 33.373045688566606], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-119318": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-119318.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 31.1, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 235.3666019529799}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-119318.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 11.061721092940784], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 13.452339590621303, 14.27137730285365, 1007.5, 465.5, 28.9, 1.0, 32.53903467070285], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-122940": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-122940.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 21.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.8}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-122940.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.499591865449531], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.614531024692203, 9.935137627948563, 1007.5, 465.5, 28.9, 2.0, 34.14516221671532], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-123460": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-123460.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 21.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 15.1}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-123460.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 11.486361181534924], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 7.002648804689358, 9.035428436922595, 1007.5, 465.5, 28.9, 4.0, 30.980644073985808], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-125351": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-125351.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 18.1, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.4}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 87.76909913957142}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-125351.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.20037235767667], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 15.728810632907548, 14.432362915828438, 1007.5, 465.5, 28.9, 1.0, 32.209305813384475], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-135700": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-135700.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 22.3, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 20.4}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 116.83058590000026}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-135700.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 11.720889096563551], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.366865884078248, 32.81958945530194, 1007.5, 465.5, 28.9, 4.0, 32.64538588802118], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-136749": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-136749.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 50.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 342.8707648044091}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-136749.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.11498030183943], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 23.846254467677035, 28.125077526297048, 1007.5, 465.5, 28.9, 4.0, 31.380596779553382], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-139908": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-139908.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 25.6, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 22.4}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 175.53819827914327}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-139908.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 8.18049159191331], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.458727462824926, 12.218559022279688, 1007.5, 465.5, 28.9, 4.0, 35.14133363623342], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-146210": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-146210.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 50.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 336.4308958706709}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-146210.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.415508412286997], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 18.078375203594, 22.394531790948683, 1007.5, 465.5, 28.9, 2.0, 32.78555804500283], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-149402": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-149402.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 26.3, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 21.9}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 175.53819827914327}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-149402.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 11.562799355338816], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.538772230982593, 14.973972105470072, 1007.5, 465.5, 28.9, 2.0, 33.88091769574508], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-159251": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-159251.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 21.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.7}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-159251.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.273609306132748], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 7.720358446134277, 9.607504562752144, 1007.5, 465.5, 28.9, 2.0, 33.07063880574108], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-163360": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-163360.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 16.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 15.5}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 91.92384939469892}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-163360.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 7.305406602357659], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 10.269078961753872, 6.626809390151488, 1007.5, 465.5, 28.9, 1.0, 39.298241830042016], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-164698": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-164698.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 23.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 21.8}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 156.63408802027698}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-164698.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.4337729265513], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 12.351274757189532, 8.687496692797236, 1007.5, 465.5, 28.9, 1.0, 32.61722351263917], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-16597": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-16597.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 19.3, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 15.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 102.72620005803094}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-16597.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.834508510839068], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.835656164669563, 15.470749854158946, 1007.5, 465.5, 28.9, 7.0, 36.02510630164952], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-174340": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-174340.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 27.7, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 22.4}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 175.53819827914327}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-174340.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 11.403472557866824], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.192226764861791, 10.23782222068794, 1007.5, 465.5, 28.9, 3.0, 30.32137449441208], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-174488": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-174488.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 26.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 21.7}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 156.63408802027698}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-174488.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 11.66761041839657], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 9.779045355845172, 7.968070631355679, 1007.5, 465.5, 28.9, 3.0, 32.37913226086214], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-177474": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-177474.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 12.1, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 10.4}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 63.35994139069634}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-177474.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.04534855685528], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 10.950928059377064, 11.912094772703778, 1007.5, 465.5, 28.9, 2.0, 32.13961532284137], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-185734": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-185734.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 24.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 18.3}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 156.63408802027698}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-185734.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 6.500834865802282], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.812857496805348, 9.803732505211345, 1007.5, 465.5, 28.9, 3.0, 33.25547788845874], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-201841": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-201841.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 32.9, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 235.36660195297992}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-201841.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 12.68703018307905], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 8.704766777864728, 9.953512840861736, 1007.5, 465.5, 28.9, 4.0, 32.13009217413889], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-210179": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-210179.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 22.6, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 19.7}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 156.63408802027698}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-210179.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.989416388139151], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 12.076456538938157, 9.908707402507954, 1007.5, 465.5, 28.9, 2.0, 31.49158163192721], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-221347": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-221347.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 22.9, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 18.7}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 156.63408802027698}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-221347.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 8.614755825880785], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.531086150490191, 11.258677762444098, 1007.5, 465.5, 28.9, 3.0, 32.41798973054942], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-230010": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-230010.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 37.9, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 285.8468129046808}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-230010.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 6.959030255986259], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 19.75950434359097, 17.409307423042808, 1007.5, 465.5, 28.9, 1.0, 31.06417355127553], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-230680": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-230680.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 50.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 455.46449671836865}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-230680.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.42931766787398], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 54.91456768606889, 42.12084804231098, 1007.5, 465.5, 28.9, 5.0, 30.287102626542403], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-231329": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-231329.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 17.7, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 13.8}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 63.35994139069634}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-231329.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 6.100430962270534], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 15.620431347925145, 9.371680934026989, 1007.5, 465.5, 28.9, 4.0, 36.48000511524841], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-233158": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-233158.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 20.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.4}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-233158.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.82972063535884], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 8.895158908948671, 8.815097764346298, 1007.5, 465.5, 28.9, 2.0, 33.761962602816865], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-249559": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-249559.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 20.6, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.7}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-249559.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 7.396698204213714], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 12.107612503558386, 11.09571984273195, 1007.5, 465.5, 28.9, 2.0, 34.39366478779125], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-250074": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-250074.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 27.9, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 20.7}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 175.53819827914327}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-250074.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.817910833958258], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 16.789764330650463, 13.12188045274152, 1007.5, 465.5, 28.9, 4.0, 33.661093850384965], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-250187": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-250187.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 31.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 24.1}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 226.01841387894368}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-250187.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 11.06756774339503], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.44563286759251, 8.25310967444649, 1007.5, 465.5, 28.9, 2.0, 27.63306230028055], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-251607": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-251607.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 31.7, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 235.36660195297992}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-251607.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.076400088875204], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 10.658921006328164, 12.337109822301125, 1007.5, 465.5, 28.9, 1.0, 30.80266601174376], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-255425": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-255425.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 24.3, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.9}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 127.96630785793164}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-255425.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.303226969189474], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 21.19990339980676, 18.839209291355782, 1007.5, 465.5, 28.9, 1.999999999999999, 34.03128540069077], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-266469": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-266469.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 26.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 20.1}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-266469.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 7.500512879645868], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 12.50795925541043, 9.133086370046886, 1007.5, 465.5, 28.9, 8.0, 34.72366516161649], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-268127": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-268127.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 27.3, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 21.6}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 156.63408802027698}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-268127.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 8.367341841153387], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 13.06366478594012, 10.100103941705903, 1007.5, 465.5, 28.9, 3.0, 33.57375678914098], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-268480": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-268480.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 35.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 235.36660195297992}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-268480.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.893121932204089], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 16.342144420309484, 14.5362977148818, 1007.5, 465.5, 28.9, 2.0, 35.62114362392285], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-276048": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-276048.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 19.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-276048.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 11.41428065195798], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 9.978989998289796, 10.347774687513224, 1007.5, 465.5, 28.9, 1.0, 35.52537857727697], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-281598": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-281598.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 35.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 24.3}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 201.34958423912275}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-281598.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.910798177544551], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 16.983916687101452, 19.84189842737772, 1007.5, 465.5, 28.9, 4.0, 30.308179079737005], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-285433": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-285433.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 22.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 19.7}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-285433.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 8.039881371058268], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.872980822136238, 16.343277477744696, 1007.5, 465.5, 28.9, 1.0, 36.97628951503878], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-285517": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-285517.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 16.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 13.6}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 87.76909913957142}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-285517.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.685649351346113], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.144016896626576, 12.66242164725051, 1007.5, 465.5, 28.9, 3.0, 31.565110014360467], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-289795": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-289795.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 36.3, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 205.45240500449012}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-289795.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.5807550113442166], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 19.54575834911123, 23.47885532194092, 1007.5, 465.5, 28.9, 6.0, 34.42279882295949], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-29185": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-29185.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 29.3, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 22.4}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 201.34958423912275}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-29185.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.549111086019463], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 12.14870346518613, 14.035476491874276, 1007.5, 465.5, 28.9, 1.999999999999996, 28.99451798177038], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-292403": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-292403.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 14.9, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 14.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 91.92384939469892}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-292403.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.548624981693443], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 8.203757620775827, 6.781498188897848, 1007.5, 465.5, 28.9, 1.0, 32.20560307340119], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-293364": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-293364.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 35.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 235.36660195297992}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-293364.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.713684490864283], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 17.924165085936608, 12.455734877674477, 1007.5, 465.5, 28.9, 2.0, 31.21972660828044], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-30554": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-30554.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 23.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 18.6}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-30554.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.85934158433267], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 12.567163997719872, 10.139257776526398, 1007.5, 465.5, 28.9, 2.0, 32.63694751703833], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-320787": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-320787.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 26.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 19.8}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 175.53819827914327}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-320787.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.651422102159652], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 9.987649350553813, 11.173314853572798, 1007.5, 465.5, 28.9, 1.0, 29.560431015163203], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-322301": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-322301.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 34.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 205.45240500449012}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-322301.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.114136594681414], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 13.966747278683346, 15.65256582539261, 1007.5, 465.5, 28.9, 3.0, 31.43612775848149], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-324449": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-324449.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 49.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 336.4308958706709}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-324449.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 8.902645593142896], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 22.52087004420612, 22.098265527686028, 1007.5, 465.5, 28.9, 2.0, 31.69982171484469], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-32691": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-32691.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 26.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 21.6}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 175.53819827914327}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-32691.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.473574756582844], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 8.399660238322193, 12.789536875314798, 1007.5, 465.5, 28.9, 2.0, 28.739136544103506], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-330722": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-330722.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 32.9, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 235.36660195297992}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-330722.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 13.050561223231895], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 9.257026896247869, 11.400704764111987, 1007.5, 465.5, 28.9, 4.0, 30.85898964271842], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-333139": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-333139.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 38.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 255.93260925576897}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-333139.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.288191170806448], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.211475265491098, 14.9575552060048, 1007.5, 465.5, 28.9, 4.0, 30.963856259796977], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-33697": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-33697.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 20.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 18.6}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-33697.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 7.036078740902563], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 17.66112910728007, 11.81303779744724, 1007.5, 465.5, 28.9, 1.0, 34.94666898470508], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-35144": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-35144.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 21.3, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 13.8}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 87.76909913957142}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-35144.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 6.433664168225855], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 18.58523848747376, 15.578703780414752, 1007.5, 465.5, 28.9, 1.0, 32.351861278341666], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-358862": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-358862.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 21.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.3}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 113.00920693947204}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-358862.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 11.282381699184803], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 16.2722202663679, 12.064316866075297, 1007.5, 465.5, 28.9, 2.0, 33.231590038026084], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-360202": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-360202.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 27.9, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 22.5}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 168.215450954481}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-360202.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.074038982067853], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 28.70278920942136, 23.776916862007468, 1007.5, 465.5, 28.9, 3.0, 31.26954348588988], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-370126": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-370126.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 29.1, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 23.8}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 201.34958423912275}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-370126.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 12.754692471218211], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 15.361245872572932, 19.616313537942453, 1007.5, 465.5, 28.9, 1.0, 30.076068489977363], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-375189": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-375189.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 22.7, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 19.3}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-375189.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 12.048024748396434], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.837244343109443, 8.593043149082693, 1007.5, 465.5, 28.9, 2.0, 33.979090160832335], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-375514": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-375514.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 19.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.5}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 91.92384939469892}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-375514.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 4.970265459676213], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 15.449186126552044, 12.932566363647927, 1007.5, 465.5, 28.9, 1.0, 34.55444760772571], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-378607": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-378607.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 20.7, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 17.7}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-378607.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 8.29183593782377], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 9.751493222089712, 9.421581773353912, 1007.5, 465.5, 28.9, 1.0, 29.324576459767], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-384336": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-384336.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 11.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 7.5}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 42.23996092713105}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-384336.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.373658617347058], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 10.845512954855614, 13.788488780736914, 1007.5, 465.5, 28.9, 3.99999999999999, 34.231224439684496], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-384936": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-384936.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 21.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.5}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 91.92384939469892}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-384936.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 4.482496758199775], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.910682834603923, 6.591359893855869, 1007.5, 465.5, 28.9, 6.0, 36.27493707288984], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-38608": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-38608.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 30.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 22.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 171.43538240220386}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-38608.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.522716886378362], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 19.450383910946496, 10.633622594040745, 1007.5, 465.5, 28.9, 3.0, 29.663866095231946], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-402352": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-402352.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 33.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 235.3666019529799}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-402352.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 7.695588228627336], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 15.975924678386736, 14.689415063765065, 1007.5, 465.5, 28.9, 1.0, 32.53786480565354], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-405269": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-405269.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 34.7, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 226.01841387894368}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-405269.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.695296055447145], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 18.06013909340126, 18.12301559068646, 1007.5, 465.5, 28.9, 3.0, 30.86906158252433], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-409012": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-409012.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 27.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 23.4}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 168.215450954481}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-409012.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 4.90064023911783], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 23.9608850784434, 22.9887624832919, 1007.5, 465.5, 28.9, 2.0, 33.44254606617062], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-418097": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-418097.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 22.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 18.5}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 138.3012491175618}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-418097.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 11.439742614324372], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 12.151761201703833, 14.243009556023232, 1007.5, 465.5, 28.9, 2.0, 28.73180302899244], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-42693": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-42693.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 36.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 235.36660195297992}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-42693.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.61193570106514], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 12.49334302977118, 13.392312202077267, 1007.5, 465.5, 28.9, 3.0, 31.129602135342207], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-432220": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-432220.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 21.1, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.1}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-432220.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.340702655369691], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 6.9231008949233805, 7.28149662546992, 1007.5, 465.5, 28.9, 3.0, 31.20926026481198], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-43404": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-43404.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 44.7, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 276.6024982351226}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-43404.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.892110130952632], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 19.47131069318616, 24.587763996106663, 1007.5, 465.5, 28.9, 5.0, 32.15677186975728], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-434902": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-434902.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 34.7, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 226.01841387894368}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-434902.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.221978636530826], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 17.997971406400087, 16.31105394490182, 1007.5, 465.5, 28.9, 5.0, 31.95449478447753], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-443866": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-443866.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 20.9, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.3}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-443866.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 7.588573324186327], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.302398294519625, 10.897194163408969, 1007.5, 465.5, 28.9, 2.0, 37.54091987997562], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-450739": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-450739.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 24.6, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 15.9}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 114.29025493480259}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-450739.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.38569417482104], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 18.830913223122383, 18.837348426428303, 1007.5, 465.5, 28.9, 3.99999999999999, 30.39533680305104], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-457349": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-457349.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 18.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 18.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-457349.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 8.102039582775523], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.245426685466828, 10.363411564592903, 1007.5, 465.5, 28.9, 1.0, 34.61452218758041], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-458062": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-458062.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 29.1, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 22.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 201.34958423912275}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-458062.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.76847364384078], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.767150786535868, 16.89134534320341, 1007.5, 465.5, 28.9, 3.0, 29.303856205418143], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-464108": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-464108.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": [], "inactive_actions": [], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": {"type": "citylearn.energy_model.StorageTank", "autosize": true}, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 28.7, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 23.7}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 205.45240500449012}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-464108.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 7.0774513767782405], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.45501025448761, 13.070755813908391, 1007.5, 465.5, 28.9, 2.0, 31.08480427439016], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-465817": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-465817.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 14.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 14.3}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 91.92384939469892}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-465817.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 13.150720637366122], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 5.938532304657043, 5.894658738733654, 1007.5, 465.5, 28.9, 3.0, 32.59838618364683], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-477303": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-477303.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 22.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 15.5}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 87.76909913957142}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-477303.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.339250499372788], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.919636675616047, 18.51152163512267, 1007.5, 465.5, 28.9, 5.0, 33.58042504028008], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-479298": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-479298.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 21.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 17.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-479298.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 8.403496780529501], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.409805495563509, 10.854721883253031, 1007.5, 465.5, 28.9, 4.0, 34.34862572739343], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-482102": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-482102.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 23.6, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 17.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 87.76909913957142}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-482102.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.556318176196315], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 20.25280325598983, 12.749266863874642, 1007.5, 465.5, 28.9, 3.0, 36.12324761128421], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-487161": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-487161.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 26.2, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 22.1}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 175.53819827914327}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-487161.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 12.959925809557898], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 8.368694581730153, 5.899318484255286, 1007.5, 465.5, 28.9, 4.0, 33.61894153727639], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-493114": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-493114.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 45.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 336.43089587067084}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-493114.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 14.88329931892768], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 9.374761100287776, 9.049222062206509, 1007.5, 465.5, 28.9, 3.0, 29.202812648425216], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-4976": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-4976.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 19.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 15.6}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 91.92384939469892}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-4976.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.11489518413476], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.38513552497514, 10.72377052039091, 1007.5, 465.5, 28.9, 4.0, 34.59426175786725], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-498529": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-498529.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 20.8, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 15.5}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-498529.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.610634715777552], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 9.010899273388716, 11.089590018138242, 1007.5, 465.5, 28.9, 3.0, 30.503524754995208], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-505902": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-505902.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 24.6, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 21.8}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 113.00920693947204}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-505902.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 8.984702463480701], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 21.55268074890885, 22.49365777490112, 1007.5, 465.5, 28.9, 1.0, 33.30270170695244], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-506307": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-506307.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 19.6, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 17.9}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-506307.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 11.717299072133756], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 7.796401250981984, 9.95630643002186, 1007.5, 465.5, 28.9, 3.0, 32.528840238649856], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-508005": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-508005.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 38.1, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 285.8468129046808}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-508005.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.401933762209923], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 19.984018882728456, 18.615255207893515, 1007.5, 465.5, 28.9, 3.0, 32.78411962155728], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-50813": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-50813.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 19.6, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.9}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-50813.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 11.051185146438671], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.948694126543849, 11.122447429706096, 1007.5, 465.5, 28.9, 2.0, 32.49395063949901], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-515536": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-515536.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 33.7, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 226.0184138789437}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-515536.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.103013973357587], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 19.267134151064525, 17.913663801903304, 1007.5, 465.5, 28.9, 4.0, 34.43712970859705], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-529678": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-529678.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 28.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 21.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 175.53819827914327}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-529678.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 4.777585340239737], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 15.670816372533876, 14.271984114753645, 1007.5, 465.5, 28.9, 3.0, 33.36606306998274], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-530480": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-530480.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 26.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 21.3}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 175.53819827914327}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-530480.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.213858709156074], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 12.762679084173229, 12.450141856980078, 1007.5, 465.5, 28.9, 4.0, 28.837691558026936], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-531043": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-531043.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 22.5, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 15.3}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 113.00920693947204}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-531043.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.397185874533056], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.58854673018152, 14.493226099257551, 1007.5, 465.5, 28.9, 2.0, 30.801553703538254], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-534588": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-534588.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 37.3, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 285.8468129046808}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-534588.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 13.296240425794586], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 13.384514697306017, 10.911146439453438, 1007.5, 465.5, 28.9, 4.0, 29.332902346096965], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-538198": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-538198.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 24.1, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 16.3}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 127.96630785793165}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-538198.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 8.627892862429965], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 15.502306533187314, 17.00302958062676, 1007.5, 465.5, 28.9, 1.999999999999999, 31.16915603248918], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-55542": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-55542.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 25.3, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 20.6}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-55542.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 11.357643429102511], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 10.930234457145266, 11.052674244376313, 1007.5, 465.5, 28.9, 5.0, 36.93226640157266], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-66099": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-66099.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 50.0, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 402.6991605679938}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-66099.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.494507738957193], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 21.61477114091716, 22.483884534630107, 1007.5, 465.5, 28.9, 1.0, 30.975356517631766], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-66303": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-66303.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 15.4, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 13.4}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 95.6631238736862}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-66303.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 7.093006110994064], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 5.997162412086362, 6.103725384924827, 1007.5, 465.5, 28.9, 2.0, 32.48435325716818], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-70189": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-70189.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 20.1, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 17.3}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 121.83805123161767}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-70189.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 8.055768448668276], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 12.12889205983019, 8.416112447674543, 1007.5, 465.5, 28.9, 2.0, 34.65779697483988], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-82426": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-82426.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 32.9, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 226.01841387894368}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-82426.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 12.027683170233937], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 9.957860738909073, 7.42131318864157, 1007.5, 465.5, 28.9, 2.0, 28.80302774994876], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-9634": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-9634.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 21.7, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 19.8}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 126.71988278139312}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-9634.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.089463048275112], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 12.689815375965448, 9.81176608469418, 1007.5, 465.5, 28.9, 1.0, 37.97518358275081], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}, "resstock-amy2018-2021-release-1-97851": {"type": "citylearn.building.LSTMDynamicsBuilding", "include": true, "energy_simulation": "resstock-amy2018-2021-release-1-97851.csv", "weather": "weather.csv", "carbon_intensity": null, "pricing": null, "inactive_observations": ["dhw_storage_soc", "dhw_storage_electricity_consumption"], "inactive_actions": ["dhw_storage"], "cooling_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "heating_device": {"type": "citylearn.energy_model.HeatPump", "autosize": true}, "dhw_device": {"type": "citylearn.energy_model.ElectricHeater", "autosize": true}, "cooling_storage": null, "heating_storage": null, "dhw_storage": null, "electrical_storage": {"type": "citylearn.energy_model.Battery", "autosize": false, "attributes": {"capacity": 47.9, "efficiency": 0.9, "capacity_loss_coefficient": 1e-05, "loss_coefficient": 0.0, "nominal_power": 25.0}}, "pv": {"type": "citylearn.energy_model.PV", "autosize": true, "autosize_attributes": {"epw_filepath": "weather.epw", "roof_area": 372.7849666413285}}, "dynamics": {"type": "citylearn.dynamics.LSTMDynamics", "attributes": {"input_size": null, "hidden_size": 8, "num_layers": 2, "lookback": 13, "filename": "resstock-amy2018-2021-release-1-97851.pth", "input_normalization_minimum": [-1.0, -1.0, -1.0, -1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 9.093347464879326], "input_normalization_maximum": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 30.36162973705169, 22.98937608586749, 1007.5, 465.5, 28.9, 1.0, 31.39823669685438], "input_observation_names": ["month_sin", "month_cos", "day_type_sin", "day_type_cos", "hour_sin", "hour_cos", "cooling_demand", "heating_demand", "direct_solar_irradiance", "diffuse_solar_irradiance", "outdoor_dry_bulb_temperature", "occupant_count", "indoor_dry_bulb_temperature"]}}}}}